<svg width="839" height="404" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" overflow="hidden"><g><rect x="0" y="0" width="839" height="403.667" fill="#FFFFFF"/><path d="M9.50005 12.6442C9.50005 9.79976 11.8078 7.49387 14.6546 7.49387L824.345 7.49387C827.192 7.49387 829.5 9.79976 829.5 12.6442L829.5 129.239C829.5 132.083 827.192 134.389 824.345 134.389L14.6546 134.389C11.8078 134.389 9.50005 132.083 9.50005 129.239Z" stroke="#163E64" stroke-miterlimit="8" fill="#77B0F8" fill-rule="evenodd"/><text font-family="Microsoft YaHei,Microsoft YaHei_MSFontService,sans-serif" font-weight="700" font-size="19" transform="matrix(1 0 0 0.999176 375.746 25)">Interface</text><path d="M17.5001 43.99C17.5001 39.285 21.3174 35.4708 26.0263 35.4708L221.974 35.4708C226.683 35.4708 230.5 39.285 230.5 43.99L230.5 121.873C230.5 126.578 226.683 130.392 221.974 130.392L26.0263 130.392C21.3174 130.392 17.5001 126.578 17.5001 121.873Z" stroke="#163E64" stroke-width="0.666667" stroke-miterlimit="8" fill="#F2F7FC" fill-rule="evenodd"/><path d="M241.5 42.9908C241.5 38.2858 245.317 34.4716 250.026 34.4716L556.974 34.4716C561.683 34.4716 565.5 38.2858 565.5 42.9908L565.5 120.874C565.5 125.579 561.683 129.393 556.974 129.393L250.026 129.393C245.317 129.393 241.5 125.579 241.5 120.874Z" stroke="#163E64" stroke-width="0.666667" stroke-miterlimit="8" fill="#F2F7FC" fill-rule="evenodd"/><text font-family="Microsoft YaHei,Microsoft YaHei_MSFontService,sans-serif" font-weight="700" font-size="17" transform="matrix(1 0 0 0.999176 54.7892 52)">User Interaction</text><text font-family="Microsoft YaHei,Microsoft YaHei_MSFontService,sans-serif" font-weight="700" font-size="17" transform="matrix(1 0 0 0.999176 337.125 52)">High</text><text font-family="Microsoft YaHei,Microsoft YaHei_MSFontService,sans-serif" font-weight="700" font-size="17" transform="matrix(1 0 0 0.999176 378.459 52)">-</text><text font-family="Microsoft YaHei,Microsoft YaHei_MSFontService,sans-serif" font-weight="700" font-size="17" transform="matrix(1 0 0 0.999176 385.959 52)">Level API</text><path d="M575.5 42.3691C575.5 38.0075 579.039 34.4716 583.404 34.4716L815.596 34.4716C819.961 34.4716 823.5 38.0075 823.5 42.3691L823.5 121.496C823.5 125.857 819.961 129.393 815.596 129.393L583.404 129.393C579.039 129.393 575.5 125.857 575.5 121.496Z" stroke="#163E64" stroke-width="0.666667" stroke-miterlimit="8" fill="#F2F7FC" fill-rule="evenodd"/><text font-family="Microsoft YaHei,Microsoft YaHei_MSFontService,sans-serif" font-weight="700" font-size="17" transform="matrix(1 0 0 0.999176 584.545 52)">Application &amp; Deployment</text><text font-family="Arial,Arial_MSFontService,sans-serif" font-weight="400" font-size="14" transform="matrix(1 0 0 0.999176 50.2933 78)">•</text><text font-family="Microsoft YaHei,Microsoft YaHei_MSFontService,sans-serif" font-weight="400" font-size="14" transform="matrix(1 0 0 0.999176 71.2933 78)">Forecasting Tasks</text><text font-family="Arial,Arial_MSFontService,sans-serif" font-weight="400" font-size="14" transform="matrix(1 0 0 0.999176 50.2933 97)">•</text><text font-family="Microsoft YaHei,Microsoft YaHei_MSFontService,sans-serif" font-weight="400" font-size="14" transform="matrix(1 0 0 0.999176 71.2933 97)">Prototyping</text><text font-family="Arial,Arial_MSFontService,sans-serif" font-weight="400" font-size="14" transform="matrix(1 0 0 0.999176 50.2933 115)">•</text><text font-family="Microsoft YaHei,Microsoft YaHei_MSFontService,sans-serif" font-weight="400" font-size="14" transform="matrix(1 0 0 0.999176 71.2933 115)">Customization</text><text font-family="Arial,Arial_MSFontService,sans-serif" font-weight="400" font-size="14" transform="matrix(1 0 0 0.999176 247.303 72)">•</text><text font-family="Microsoft YaHei,Microsoft YaHei_MSFontService,sans-serif" font-weight="400" font-size="14" transform="matrix(1 0 0 0.999176 268.303 72)">Modeler</text><text font-family="Microsoft YaHei,Microsoft YaHei_MSFontService,sans-serif" font-weight="400" font-size="14" transform="matrix(1 0 0 0.999176 268.136 86)">(</text><text font-family="Consolas,Consolas_MSFontService,sans-serif" font-weight="700" font-size="14" transform="matrix(1 0 0 0.999176 272.803 86)">modeler.py</text><text font-family="Microsoft YaHei,Microsoft YaHei_MSFontService,sans-serif" font-weight="400" font-size="14" transform="matrix(1 0 0 0.999176 349.47 86)">)</text><text font-family="Arial,Arial_MSFontService,sans-serif" font-weight="400" font-size="14" transform="matrix(1 0 0 0.999176 247.303 103)">•</text><text font-family="Microsoft YaHei,Microsoft YaHei_MSFontService,sans-serif" font-weight="400" font-size="14" transform="matrix(1 0 0 0.999176 268.303 103)">Evaluator Class</text><text font-family="Microsoft YaHei,Microsoft YaHei_MSFontService,sans-serif" font-weight="400" font-size="14" transform="matrix(1 0 0 0.999176 268.136 118)">(</text><text font-family="Consolas,Consolas_MSFontService,sans-serif" font-weight="700" font-size="14" transform="matrix(1 0 0 0.999176 272.803 118)">metric/evaluator.py</text><text font-family="Microsoft YaHei,Microsoft YaHei_MSFontService,sans-serif" font-weight="400" font-size="14" transform="matrix(1 0 0 0.999176 418.47 118)">)</text><text font-family="Arial,Arial_MSFontService,sans-serif" font-weight="400" font-size="14" transform="matrix(1 0 0 0.999176 426.911 73)">•</text><text font-family="Microsoft YaHei,Microsoft YaHei_MSFontService,sans-serif" font-weight="400" font-size="14" transform="matrix(1 0 0 0.999176 447.911 73)">Trainer Class</text><text font-family="Microsoft YaHei,Microsoft YaHei_MSFontService,sans-serif" font-weight="400" font-size="14" transform="matrix(1 0 0 0.999176 447.911 88)">(</text><text font-family="Consolas,Consolas_MSFontService,sans-serif" font-weight="700" font-size="14" transform="matrix(1 0 0 0.999176 452.578 88)">modeler.py</text><text font-family="Microsoft YaHei,Microsoft YaHei_MSFontService,sans-serif" font-weight="400" font-size="14" transform="matrix(1 0 0 0.999176 529.244 88)">)</text><text font-family="Arial,Arial_MSFontService,sans-serif" font-weight="400" font-size="14" transform="matrix(1 0 0 0.999176 426.911 105)">•</text><text font-family="Microsoft YaHei,Microsoft YaHei_MSFontService,sans-serif" font-weight="400" font-size="14" transform="matrix(1 0 0 0.999176 447.911 105)">Visualization</text><text font-family="Microsoft YaHei,Microsoft YaHei_MSFontService,sans-serif" font-weight="400" font-size="14" transform="matrix(1 0 0 0.999176 447.744 120)">(</text><text font-family="Consolas,Consolas_MSFontService,sans-serif" font-weight="700" font-size="14" transform="matrix(1 0 0 0.999176 452.411 120)">visualize.py</text><text font-family="Microsoft YaHei,Microsoft YaHei_MSFontService,sans-serif" font-weight="400" font-size="14" transform="matrix(1 0 0 0.999176 544.411 120)">)</text><text font-family="Arial,Arial_MSFontService,sans-serif" font-weight="400" font-size="14" transform="matrix(1 0 0 0.999176 610.559 73)">•</text><text font-family="Microsoft YaHei,Microsoft YaHei_MSFontService,sans-serif" font-weight="400" font-size="14" transform="matrix(1 0 0 0.999176 631.559 73)">Real</text><text font-family="Microsoft YaHei,Microsoft YaHei_MSFontService,sans-serif" font-weight="400" font-size="14" transform="matrix(1 0 0 0.999176 660.059 73)">-</text><text font-family="Microsoft YaHei,Microsoft YaHei_MSFontService,sans-serif" font-weight="400" font-size="14" transform="matrix(1 0 0 0.999176 666.059 73)">time Forecasting</text><text font-family="Arial,Arial_MSFontService,sans-serif" font-weight="400" font-size="14" transform="matrix(1 0 0 0.999176 610.559 89)">•</text><text font-family="Microsoft YaHei,Microsoft YaHei_MSFontService,sans-serif" font-weight="400" font-size="14" transform="matrix(1 0 0 0.999176 631.559 89)">Custom Solutions</text><text font-family="Arial,Arial_MSFontService,sans-serif" font-weight="400" font-size="14" transform="matrix(1 0 0 0.999176 610.559 105)">•</text><text font-family="Microsoft YaHei,Microsoft YaHei_MSFontService,sans-serif" font-weight="400" font-size="14" transform="matrix(1 0 0 0.999176 631.559 105)">Research Prototyping</text><text font-family="Arial,Arial_MSFontService,sans-serif" font-weight="400" font-size="14" transform="matrix(1 0 0 0.999176 610.559 120)">•</text><text font-family="Microsoft YaHei,Microsoft YaHei_MSFontService,sans-serif" font-weight="400" font-size="14" transform="matrix(1 0 0 0.999176 631.559 120)">Generative Modeling</text><path d="M10.5001 145.535C10.5001 142.69 12.8078 140.384 15.6546 140.384L825.345 140.384C828.192 140.384 830.5 142.69 830.5 145.535L830.5 262.129C830.5 264.974 828.192 267.28 825.345 267.28L15.6546 267.28C12.8078 267.28 10.5001 264.974 10.5001 262.129Z" stroke="#13501B" stroke-miterlimit="8" fill="#A2DB7D" fill-rule="evenodd"/><text font-family="Microsoft YaHei,Microsoft YaHei_MSFontService,sans-serif" font-weight="700" font-size="19" transform="matrix(1 0 0 0.999176 232.506 159)">Multi</text><text font-family="Microsoft YaHei,Microsoft YaHei_MSFontService,sans-serif" font-weight="700" font-size="19" transform="matrix(1 0 0 0.999176 282.339 159)">-</text><text font-family="Microsoft YaHei,Microsoft YaHei_MSFontService,sans-serif" font-weight="700" font-size="19" transform="matrix(1 0 0 0.999176 290.506 159)">level Workflow or Implementation</text><path d="M17.5001 175.881C17.5001 171.176 21.3174 167.362 26.0263 167.362L221.974 167.362C226.683 167.362 230.5 171.176 230.5 175.881L230.5 253.764C230.5 258.469 226.683 262.284 221.974 262.284L26.0263 262.284C21.3174 262.284 17.5001 258.469 17.5001 253.764Z" stroke="#13501B" stroke-width="0.666667" stroke-miterlimit="8" fill="#E7F7E1" fill-rule="evenodd"/><path d="M252.5 175.881C252.5 171.176 256.317 167.362 261.026 167.362L544.974 167.362C549.683 167.362 553.5 171.176 553.5 175.881L553.5 253.764C553.5 258.469 549.683 262.284 544.974 262.284L261.026 262.284C256.317 262.284 252.5 258.469 252.5 253.764Z" stroke="#13501B" stroke-width="0.666667" stroke-miterlimit="8" fill="#E7F7E1" fill-rule="evenodd"/><text font-family="Microsoft YaHei,Microsoft YaHei_MSFontService,sans-serif" font-weight="700" font-size="17" transform="matrix(1 0 0 0.999176 104.275 185)">Data</text><text font-family="Microsoft YaHei,Microsoft YaHei_MSFontService,sans-serif" font-weight="700" font-size="17" transform="matrix(1 0 0 0.999176 377.923 185)">Model</text><path d="M646.5 175.259C646.5 170.898 650.039 167.362 654.404 167.362L815.596 167.362C819.961 167.362 823.5 170.898 823.5 175.259L823.5 254.386C823.5 258.748 819.961 262.284 815.596 262.284L654.404 262.284C650.039 262.284 646.5 258.748 646.5 254.386Z" stroke="#13501B" stroke-width="0.666667" stroke-miterlimit="8" fill="#E7F7E1" fill-rule="evenodd"/><text font-family="Microsoft YaHei,Microsoft YaHei_MSFontService,sans-serif" font-weight="700" font-size="17" transform="matrix(1 0 0 0.999176 708.255 185)">Metric</text><text font-family="Arial,Arial_MSFontService,sans-serif" font-weight="400" font-size="14" transform="matrix(1 0 0 0.999176 31.68 200)">•</text><text font-family="Microsoft YaHei,Microsoft YaHei_MSFontService,sans-serif" font-weight="400" font-size="14" transform="matrix(1 0 0 0.999176 52.68 200)">Dataset Classes</text><text font-family="Microsoft YaHei,Microsoft YaHei_MSFontService,sans-serif" font-weight="400" font-size="14" transform="matrix(1 0 0 0.999176 52.5133 219)">(SST, SMT, MMT, BDP)</text><text font-family="Arial,Arial_MSFontService,sans-serif" font-weight="400" font-size="14" transform="matrix(1 0 0 0.999176 31.68 237)">•</text><text font-family="Microsoft YaHei,Microsoft YaHei_MSFontService,sans-serif" font-weight="400" font-size="14" transform="matrix(1 0 0 0.999176 52.68 237)">Scaling</text><text font-family="Arial,Arial_MSFontService,sans-serif" font-weight="400" font-size="14" transform="matrix(1 0 0 0.999176 31.68 256)">•</text><text font-family="Microsoft YaHei,Microsoft YaHei_MSFontService,sans-serif" font-weight="400" font-size="14" transform="matrix(1 0 0 0.999176 52.68 256)">Splitting</text><text font-family="Arial,Arial_MSFontService,sans-serif" font-weight="400" font-size="14" transform="matrix(1 0 0 0.999176 126.767 239)">•</text><text font-family="Microsoft YaHei,Microsoft YaHei_MSFontService,sans-serif" font-weight="400" font-size="14" transform="matrix(1 0 0 0.999176 147.767 239)">Patching</text><text font-family="Arial,Arial_MSFontService,sans-serif" font-weight="400" font-size="14" transform="matrix(1 0 0 0.999176 262.871 211)">•</text><text font-family="Microsoft YaHei,Microsoft YaHei_MSFontService,sans-serif" font-weight="400" font-size="14" transform="matrix(1 0 0 0.999176 283.871 211)">Base Components</text><text font-family="Arial,Arial_MSFontService,sans-serif" font-weight="400" font-size="14" transform="matrix(1 0 0 0.999176 262.871 230)">•</text><text font-family="Microsoft YaHei,Microsoft YaHei_MSFontService,sans-serif" font-weight="400" font-size="14" transform="matrix(1 0 0 0.999176 283.871 230)">Generative Models</text><text font-family="Arial,Arial_MSFontService,sans-serif" font-weight="400" font-size="14" transform="matrix(1 0 0 0.999176 262.871 248)">•</text><text font-family="Microsoft YaHei,Microsoft YaHei_MSFontService,sans-serif" font-weight="400" font-size="14" transform="matrix(1 0 0 0.999176 283.871 248)">Fusion Models</text><text font-family="Arial,Arial_MSFontService,sans-serif" font-weight="400" font-size="14" transform="matrix(1 0 0 0.999176 428.061 211)">•</text><text font-family="Microsoft YaHei,Microsoft YaHei_MSFontService,sans-serif" font-weight="400" font-size="14" transform="matrix(1 0 0 0.999176 449.061 211)">MTS Models</text><text font-family="Arial,Arial_MSFontService,sans-serif" font-weight="400" font-size="14" transform="matrix(1 0 0 0.999176 428.061 230)">•</text><text font-family="Microsoft YaHei,Microsoft YaHei_MSFontService,sans-serif" font-weight="400" font-size="14" transform="matrix(1 0 0 0.999176 449.061 230)">UTS Models</text><text font-family="Arial,Arial_MSFontService,sans-serif" font-weight="400" font-size="14" transform="matrix(1 0 0 0.999176 665.778 210)">•</text><text font-family="Microsoft YaHei,Microsoft YaHei_MSFontService,sans-serif" font-weight="400" font-size="14" transform="matrix(1 0 0 0.999176 686.778 210)">Standard Metrics</text><text font-family="Arial,Arial_MSFontService,sans-serif" font-weight="400" font-size="14" transform="matrix(1 0 0 0.999176 665.778 229)">•</text><text font-family="Microsoft YaHei,Microsoft YaHei_MSFontService,sans-serif" font-weight="400" font-size="14" transform="matrix(1 0 0 0.999176 686.778 229)">Sparse Metrics</text><text font-family="Arial,Arial_MSFontService,sans-serif" font-weight="400" font-size="14" transform="matrix(1 0 0 0.999176 665.778 247)">•</text><text font-family="Microsoft YaHei,Microsoft YaHei_MSFontService,sans-serif" font-weight="400" font-size="14" transform="matrix(1 0 0 0.999176 686.778 247)">Evaluator Class</text><path d="M230 213.824 245.085 213.824 245.085 215.822 230 215.822ZM243.752 210.826 251.752 214.823 243.752 218.82Z"/><path d="M553 188.844 639.556 188.844 639.556 190.843 553 190.843ZM638.222 185.847 646.222 189.843 638.222 193.84Z"/><path d="M1.12904e-06-0.999176 86.1738-0.999078 86.1738 0.999273-1.12904e-06 0.999176ZM84.8415-3.99661 92.8349 0.0001049 84.8415 3.9968Z" transform="matrix(-1.00083 0 0 1 645.911 240.801)"/><text font-family="Microsoft YaHei,Microsoft YaHei_MSFontService,sans-serif" font-weight="400" font-size="14" transform="matrix(1 0 0 0.999176 559.879 213)">Feedback &amp;</text><text font-family="Microsoft YaHei,Microsoft YaHei_MSFontService,sans-serif" font-weight="400" font-size="14" transform="matrix(1 0 0 0.999176 567.879 232)">Adjusting</text><path d="M10.5001 277.426C10.5001 274.581 12.8078 272.275 15.6546 272.275L825.345 272.275C828.192 272.275 830.5 274.581 830.5 277.426L830.5 394.02C830.5 396.865 828.192 399.171 825.345 399.171L15.6546 399.171C12.8078 399.171 10.5001 396.865 10.5001 394.02Z" stroke="#80350E" stroke-miterlimit="8" fill="#FBB055" fill-rule="evenodd"/><text font-family="Microsoft YaHei,Microsoft YaHei_MSFontService,sans-serif" font-weight="700" font-size="19" transform="matrix(1 0 0 0.999176 358.506 291)">Infrastructure</text><path d="M17.5001 307.772C17.5001 303.067 21.3174 299.253 26.0263 299.253L221.974 299.253C226.683 299.253 230.5 303.067 230.5 307.772L230.5 385.656C230.5 390.361 226.683 394.175 221.974 394.175L26.0263 394.175C21.3174 394.175 17.5001 390.361 17.5001 385.656Z" stroke="#80350E" stroke-width="0.666667" stroke-miterlimit="8" fill="#FDEFE7" fill-rule="evenodd"/><path d="M252.5 307.772C252.5 303.067 256.317 299.253 261.026 299.253L474.974 299.253C479.683 299.253 483.5 303.067 483.5 307.772L483.5 385.655C483.5 390.361 479.683 394.175 474.974 394.175L261.026 394.175C256.317 394.175 252.5 390.361 252.5 385.655Z" stroke="#80350E" stroke-width="0.666667" stroke-miterlimit="8" fill="#FDEFE7" fill-rule="evenodd"/><text font-family="Microsoft YaHei,Microsoft YaHei_MSFontService,sans-serif" font-weight="700" font-size="17" transform="matrix(1 0 0 0.999176 26.525 317)">Data Sources &amp; Access</text><text font-family="Microsoft YaHei,Microsoft YaHei_MSFontService,sans-serif" font-weight="700" font-size="17" transform="matrix(1 0 0 0.999176 289.801 317)">Training Utilities</text><path d="M504.5 307.15C504.5 302.789 508.039 299.253 512.404 299.253L815.596 299.253C819.961 299.253 823.5 302.789 823.5 307.15L823.5 386.277C823.5 390.639 819.961 394.175 815.596 394.175L512.404 394.175C508.039 394.175 504.5 390.639 504.5 386.277Z" stroke="#80350E" stroke-width="0.666667" stroke-miterlimit="8" fill="#FDEFE7" fill-rule="evenodd"/><text font-family="Microsoft YaHei,Microsoft YaHei_MSFontService,sans-serif" font-weight="700" font-size="17" transform="matrix(1 0 0 0.999176 527.847 318)">Model Artifacts &amp; Management</text><text font-family="Arial,Arial_MSFontService,sans-serif" font-weight="400" font-size="14" transform="matrix(1 0 0 0.999176 64.4942 346)">•</text><text font-family="Microsoft YaHei,Microsoft YaHei_MSFontService,sans-serif" font-weight="400" font-size="14" transform="matrix(1 0 0 0.999176 85.4942 346)">Local Data</text><text font-family="Arial,Arial_MSFontService,sans-serif" font-weight="400" font-size="14" transform="matrix(1 0 0 0.999176 64.4942 371)">•</text><text font-family="Microsoft YaHei,Microsoft YaHei_MSFontService,sans-serif" font-weight="400" font-size="14" transform="matrix(1 0 0 0.999176 85.4942 371)">Remote Data</text><text font-family="Arial,Arial_MSFontService,sans-serif" font-weight="400" font-size="14" transform="matrix(1 0 0 0.999176 262.125 345)">•</text><text font-family="Microsoft YaHei,Microsoft YaHei_MSFontService,sans-serif" font-weight="400" font-size="14" transform="matrix(1 0 0 0.999176 280.125 345)">Training Algorithms</text><text font-family="Arial,Arial_MSFontService,sans-serif" font-weight="400" font-size="14" transform="matrix(1 0 0 0.999176 262.125 361)">•</text><text font-family="Microsoft YaHei,Microsoft YaHei_MSFontService,sans-serif" font-weight="400" font-size="14" transform="matrix(1 0 0 0.999176 280.125 361)">Reproducibility</text><text font-family="Arial,Arial_MSFontService,sans-serif" font-weight="400" font-size="14" transform="matrix(1 0 0 0.999176 262.125 377)">•</text><text font-family="Microsoft YaHei,Microsoft YaHei_MSFontService,sans-serif" font-weight="400" font-size="14" transform="matrix(1 0 0 0.999176 280.125 377)">Visualization</text><text font-family="Arial,Arial_MSFontService,sans-serif" font-weight="400" font-size="14" transform="matrix(1 0 0 0.999176 525.633 351)">•</text><text font-family="Microsoft YaHei,Microsoft YaHei_MSFontService,sans-serif" font-weight="400" font-size="14" transform="matrix(1 0 0 0.999176 546.633 351)">Trained Model</text><text font-family="Arial,Arial_MSFontService,sans-serif" font-weight="400" font-size="14" transform="matrix(1 0 0 0.999176 525.633 376)">•</text><text font-family="Microsoft YaHei,Microsoft YaHei_MSFontService,sans-serif" font-weight="400" font-size="14" transform="matrix(1 0 0 0.999176 546.633 376)">Configurations</text><text font-family="Arial,Arial_MSFontService,sans-serif" font-weight="400" font-size="14" transform="matrix(1 0 0 0.999176 661.738 351)">•</text><text font-family="Microsoft YaHei,Microsoft YaHei_MSFontService,sans-serif" font-weight="400" font-size="14" transform="matrix(1 0 0 0.999176 682.738 351)">Checkpoints</text><text font-family="Arial,Arial_MSFontService,sans-serif" font-weight="400" font-size="14" transform="matrix(1 0 0 0.999176 661.738 376)">•</text><text font-family="Microsoft YaHei,Microsoft YaHei_MSFontService,sans-serif" font-weight="400" font-size="14" transform="matrix(1 0 0 0.999176 682.738 376)">Evaluation Results</text><text font-family="Arial,Arial_MSFontService,sans-serif" font-weight="400" font-size="14" transform="matrix(1 0 0 0.999176 377.721 377)">•</text><text font-family="Microsoft YaHei,Microsoft YaHei_MSFontService,sans-serif" font-weight="400" font-size="14" transform="matrix(1 0 0 0.999176 395.721 377)">Fine</text><text font-family="Microsoft YaHei,Microsoft YaHei_MSFontService,sans-serif" font-weight="400" font-size="14" transform="matrix(1 0 0 0.999176 423.554 377)">-</text><text font-family="Microsoft YaHei,Microsoft YaHei_MSFontService,sans-serif" font-weight="400" font-size="14" transform="matrix(1 0 0 0.999176 429.554 377)">tuning</text></g></svg>