{"global": {"seed": 2025, "device": "cpu", "gpu_ids": null, "loss": "MSE", "metrics": ["MAE", "RMSE"], "scale": null, "log_root": "{log_root}"}, "1day": {"meta": {"frequency": "1 day", "start": "2011-01-01", "end": "2020-12-31", "length": 3653, "time": "Date", "path": "../../dataset/xmcdc/outpatients_2011_2020_1day.csv", "HFMD": ["手足口病"], "ID": ["其他感染性腹泻"], "HV": ["肝炎"], "multivariate": ["手足口病", "肝炎", "其他感染性腹泻"], "weather": ["平均温度", "最高温", "最低温", "平均降水", "最高露点", "平均露点", "最低露点", "最高湿度(%)", "最低湿度(%)", "平均相对湿度(%)", "最高气压", "平均气压", "最低气压", "最高风速", "平均风速", "最低风速"], "HFMD_bsi": ["BSI_厦门手足口病_all", "BSI_厦门手足口病_pc", "BSI_厦门手足口病_wise"], "ID_bsi": ["BSI_厦门腹泻_all", "BSI_厦门腹泻_pc", "BSI_厦门腹泻_wise"], "HV_bsi": ["BSI_厦门肝炎_all", "BSI_厦门肝炎_pc", "BSI_厦门肝炎_wise"]}, "experiments": {"HFMD_L10_H1": {"targets": "HFMD", "exogenous": ["weather", "HFMD_bsi"], "split": {"input_window_size": 10, "output_window_size": 1, "horizon": 1, "stride": 1, "train_ratio": 0.8}, "trainer": {"optimizer": {"lr": 0.0002, "weight_decay": 0.0}, "lr_scheduler": {"step_size": 10, "gamma": 0.5}, "epoch_range": [1, 500], "early_stop": {"patience": 7, "delta": 0.01, "mode": "rel"}, "batch_size": 32, "shuffle": true}, "models": [["gar", {"activation": "linear"}], ["ar", {"activation": "linear"}], ["var", {"activation": "linear"}], ["ann", {"hidden_size": 32}], ["drn", {"hidden_size": 64, "number_stacks": 7, "number_blocks_per_stack": 1, "use_rnn": false}], ["rnn", {"rnn_cls": "gru", "hidden_size": 32, "num_layers": 1, "bidirectional": false, "dropout_rate": 0.0, "decoder_way": "mapping"}], ["ed", {"rnn_cls": "gru", "hidden_size": 32, "num_layers": 1, "bidirectional": false, "dropout_rate": 0.0, "decoder_way": "mapping"}], ["tcn", {"num_channels": [32], "kernel_size": 2, "dropout_rate": 0.2}], ["cnnrnn", {"cnn_out_channels": 50, "cnn_kernel_size": 9, "rnn_cls": "gru", "rnn_hidden_size": 32, "rnn_num_layers": 1, "rnn_bidirectional": false, "dropout_rate": 0.0, "decoder_way": "mapping"}], ["cnnrnnres", {"cnn_out_channels": 50, "cnn_kernel_size": 9, "rnn_cls": "gru", "rnn_hidden_size": 32, "rnn_num_layers": 1, "rnn_bidirectional": false, "dropout_rate": 0.0, "decoder_way": "mapping", "residual_window_size": 5, "residual_ratio": 0.1}], ["lstnet", {"cnn_out_channels": 50, "cnn_kernel_size": 9, "rnn_hidden_size": 50, "rnn_num_layers": 1, "skip_window_size": 24, "skip_gru_hidden_size": 20, "highway_window_size": 24, "dropout_rate": 0.0}], ["nhits", {"n_blocks": [1, 1, 1], "n_layers": [2, 2, 2, 2, 2, 2, 2, 2], "hidden_size": [[512, 512], [512, 512], [512, 512]], "pooling_sizes": [8, 8, 8], "downsample_frequencies": [24, 12, 1], "pooling_mode": "max", "interpolation_mode": "linear", "dropout": 0.0, "activation": "ReLU", "initialization": "lecun_normal", "batch_normalization": false, "shared_weights": false, "naive_level": true}], ["dlinear", {"individual": false, "kernel_size": 7}], ["nlinear", {"individual": false}], ["transformer", {"label_window_size": 10, "d_model": 256, "num_heads": 8, "num_encoder_layers": 1, "num_decoder_layers": 1, "dim_ff": 1024, "dropout_rate": 0.0}], ["informer", {"label_window_size": 10, "d_model": 256, "num_heads": 8, "num_encoder_layers": 2, "num_decoder_layers": 1, "dim_ff": 1024, "dropout_rate": 0.05}], ["autoformer", {"label_window_size": 10, "d_model": 256, "num_heads": 8, "num_encoder_layers": 1, "num_decoder_layers": 1, "dim_ff": 1024, "dropout_rate": 0.0, "moving_avg": 7}], ["fedformer", {"label_window_size": 10, "d_model": 256, "num_heads": 8, "num_encoder_layers": 1, "num_decoder_layers": 1, "dim_ff": 1024, "activation": "relu", "moving_avg": 7, "dropout_rate": 0.0, "version": "fourier", "mode_select": "random", "modes": 32}], ["film", {"d_model": 128, "use_instance_scale": true}], ["triformer", {"channels": 32, "patch_sizes": [5, 2], "mem_dim": 5}], ["crossformer", {"d_model": 128, "num_heads": 8, "num_encoder_layers": 1, "num_decoder_layers": 1, "dim_ff": 512, "dropout_rate": 0.0, "factor": 5, "seg_len": 24, "win_size": 2}], ["timesnet", {"d_model": 512, "num_encoder_layers": 1, "dim_ff": 256, "dropout_rate": 0.0, "num_kernels": 6, "top_k": 5, "use_instance_scale": false}], ["patchtst", {"d_model": 64, "num_heads": 8, "num_encoder_layers": 6, "dim_ff": 256, "patch_len": 5, "patch_stride": 2, "patch_padding": 2, "use_instance_scale": true}], ["s<PERSON><PERSON><PERSON><PERSON>", {"input_dim": 1, "output_dim": 1, "input_embedding_dim": 24, "tod_steps_per_day": 24, "tod_embedding_dim": 0, "dow_embedding_dim": 0, "spatial_embedding_dim": 0, "adaptive_embedding_dim": 80, "dim_ff": 256, "num_heads": 4, "num_layers": 3, "dropout_rate": 0.1, "use_mixed_proj": true}], ["itransformer", {"d_model": 128, "num_heads": 2, "num_encoder_layers": 6, "dim_ff": 512, "activation": "relu", "dropout_rate": 0.0, "use_instance_scale": true}], ["timesfm", {"d_model": 64, "num_heads": 2, "num_layers": 2, "dim_ff": 256, "dropout_rate": 0.0}], ["timer", {"patch_len": 4, "d_model": 64, "num_heads": 8, "e_layers": 1, "dim_ff": 512, "activation": "relu", "dropout_rate": 0.0}], ["timexer", {"d_model": 512, "num_heads": 8, "num_encoder_layers": 2, "dim_ff": 256, "dropout_rate": 0.05, "activation": "relu", "patch_len": 5, "use_instance_scale": true}], ["timemixer", {"d_model": 16, "num_heads": 8, "num_encoder_layers": 1, "dim_ff": 256, "dropout_rate": 0.05, "moving_avg": 25, "top_k": 5, "channel_independence": true, "decomposition_method": "moving_avg", "down_sampling_method": "avg", "down_sampling_window": 1, "down_sampling_layers": 1, "use_instance_scale": true}], ["stid", {"node_dim": 32, "embed_dim": 1024, "input_dim": 1, "num_layer": 1, "if_node": true}], ["stnorm", {"tnorm_bool": true, "snorm_bool": true, "channels": 16, "kernel_size": 2, "blocks": 1, "layers": 2}], ["magnet", {"label_window_size": 48, "conv2d_in_channels": 1, "residual_channels": 32, "conv_channels": 32, "skip_channels": 4, "end_channels": 128, "node_dim": 40, "tanhalpha": 3.0, "static_feat": null, "dilation_exponential": 1, "kernel_size": 7, "gcn_depth": 2, "gcn_true": false, "propalpha": 0.05, "layer_norm_affline": true, "buildA_true": true, "predefined_A": null, "dropout": 0.3}], ["gwn", {"out_dim": 1, "supports": null, "gcn_bool": true, "addaptadj": true, "aptinit": null, "in_dim": 1, "residual_channels": 32, "dilation_channels": 32, "skip_channels": 256, "end_channels": 512, "kernel_size": 2, "blocks": 1, "layers": 2, "dropout": 0.3}], ["fgnn", {"embed_size": 128, "hidden_size": 256, "hard_thresholding_fraction": 1, "hidden_size_factor": 1, "sparsity_threshold": 0.01}], ["coat", {"mode": "dr", "activation": "linear", "use_instance_scale": false, "dropout_rate": 0.0}], ["gain", {"gat_hidden_size": 64, "gat_nhead": 128, "gru_hidden_size": 8, "gru_num_layers": 1, "cnn_kernel_size": 3, "cnn_out_channels": 16, "highway_window_size": 10, "dropout_rate": 0.5}]]}, "ID_L10_H1": {"targets": "ID"}}}, "1week": {"meta": {"frequency": "1 day", "start": "2011-01-07", "end": "2021-01-08", "length": 521, "time": "Date", "path": "../../dataset/xmcdc/outpatients_2011_2020_1week.csv", "HFMD": ["手足口病"], "ID": ["其他感染性腹泻"], "HV": ["肝炎"], "multivariate": ["手足口病", "肝炎", "其他感染性腹泻"], "weather": ["平均温度", "最高温", "最低温", "平均降水", "最高露点", "平均露点", "最低露点", "最高湿度(%)", "最低湿度(%)", "平均相对湿度(%)", "最高气压", "平均气压", "最低气压", "最高风速", "平均风速", "最低风速"], "HFMD_bsi": ["BSI_厦门手足口病_all", "BSI_厦门手足口病_pc", "BSI_厦门手足口病_wise"], "ID_bsi": ["BSI_厦门腹泻_all", "BSI_厦门腹泻_pc", "BSI_厦门腹泻_wise"], "HV_bsi": ["BSI_厦门肝炎_all", "BSI_厦门肝炎_pc", "BSI_厦门肝炎_wise"]}, "experiments": {"HFMD_L10_H1": {"targets": "HFMD", "split": {"input_window_size": 10, "output_window_size": 1, "horizon": 1, "stride": 1, "train_ratio": 0.8}, "trainer": {"optimizer": {"lr": 0.0002, "weight_decay": 0.0}, "lr_scheduler": {"step_size": 10, "gamma": 0.5}, "epoch_range": [1, 500], "early_stop": {"patience": 7, "delta": 0.01, "mode": "rel"}, "batch_size": 32, "shuffle": true}, "models": [["gar", {"activation": "linear"}], ["ar", {"activation": "linear"}], ["var", {"activation": "linear"}], ["ann", {"hidden_size": 32}], ["drn", {"hidden_size": 64, "number_stacks": 7, "number_blocks_per_stack": 1, "use_rnn": false}], ["rnn", {"rnn_cls": "gru", "hidden_size": 32, "num_layers": 1, "bidirectional": false, "dropout_rate": 0.0, "decoder_way": "mapping"}], ["ed", {"rnn_cls": "gru", "hidden_size": 32, "num_layers": 1, "bidirectional": false, "dropout_rate": 0.0, "decoder_way": "mapping"}], ["tcn", {"num_channels": [32], "kernel_size": 2, "dropout_rate": 0.2}], ["cnnrnn", {"cnn_out_channels": 50, "cnn_kernel_size": 9, "rnn_cls": "gru", "rnn_hidden_size": 32, "rnn_num_layers": 1, "rnn_bidirectional": false, "dropout_rate": 0.0, "decoder_way": "mapping"}], ["cnnrnnres", {"cnn_out_channels": 50, "cnn_kernel_size": 9, "rnn_cls": "gru", "rnn_hidden_size": 32, "rnn_num_layers": 1, "rnn_bidirectional": false, "dropout_rate": 0.0, "decoder_way": "mapping", "residual_window_size": 5, "residual_ratio": 0.1}], ["lstnet", {"cnn_out_channels": 50, "cnn_kernel_size": 9, "rnn_hidden_size": 50, "rnn_num_layers": 1, "skip_window_size": 24, "skip_gru_hidden_size": 20, "highway_window_size": 24, "dropout_rate": 0.0}], ["nhits", {"n_blocks": [1, 1, 1], "n_layers": [2, 2, 2, 2, 2, 2, 2, 2], "hidden_size": [[512, 512], [512, 512], [512, 512]], "pooling_sizes": [8, 8, 8], "downsample_frequencies": [24, 12, 1], "pooling_mode": "max", "interpolation_mode": "linear", "dropout": 0.0, "activation": "ReLU", "initialization": "lecun_normal", "batch_normalization": false, "shared_weights": false, "naive_level": true}], ["dlinear", {"individual": false, "kernel_size": 7}], ["nlinear", {"individual": false}], ["transformer", {"label_window_size": 10, "d_model": 256, "num_heads": 8, "num_encoder_layers": 1, "num_decoder_layers": 1, "dim_ff": 1024, "dropout_rate": 0.0}], ["informer", {"label_window_size": 10, "d_model": 256, "num_heads": 8, "num_encoder_layers": 2, "num_decoder_layers": 1, "dim_ff": 1024, "dropout_rate": 0.05}], ["autoformer", {"label_window_size": 10, "d_model": 256, "num_heads": 8, "num_encoder_layers": 1, "num_decoder_layers": 1, "dim_ff": 1024, "dropout_rate": 0.0, "moving_avg": 7}], ["fedformer", {"label_window_size": 10, "d_model": 256, "num_heads": 8, "num_encoder_layers": 1, "num_decoder_layers": 1, "dim_ff": 1024, "activation": "relu", "moving_avg": 7, "dropout_rate": 0.0, "version": "fourier", "mode_select": "random", "modes": 32}], ["film", {"d_model": 128, "use_instance_scale": true}], ["triformer", {"channels": 32, "patch_sizes": [5, 2], "mem_dim": 5}], ["crossformer", {"d_model": 128, "num_heads": 8, "num_encoder_layers": 1, "num_decoder_layers": 1, "dim_ff": 512, "dropout_rate": 0.0, "factor": 5, "seg_len": 24, "win_size": 2}], ["timesnet", {"d_model": 512, "num_encoder_layers": 1, "dim_ff": 256, "dropout_rate": 0.0, "num_kernels": 6, "top_k": 5, "use_instance_scale": false}], ["patchtst", {"d_model": 64, "num_heads": 8, "num_encoder_layers": 6, "dim_ff": 256, "patch_len": 5, "patch_stride": 2, "patch_padding": 2, "use_instance_scale": true}], ["s<PERSON><PERSON><PERSON><PERSON>", {"input_dim": 1, "output_dim": 1, "input_embedding_dim": 24, "tod_steps_per_day": 24, "tod_embedding_dim": 0, "dow_embedding_dim": 0, "spatial_embedding_dim": 0, "adaptive_embedding_dim": 80, "dim_ff": 256, "num_heads": 4, "num_layers": 3, "dropout_rate": 0.1, "use_mixed_proj": true}], ["itransformer", {"d_model": 128, "num_heads": 2, "num_encoder_layers": 6, "dim_ff": 512, "activation": "relu", "dropout_rate": 0.0, "use_instance_scale": true}], ["timesfm", {"d_model": 64, "num_heads": 2, "num_layers": 2, "dim_ff": 256, "dropout_rate": 0.0}], ["timer", {"patch_len": 4, "d_model": 64, "num_heads": 8, "e_layers": 1, "dim_ff": 512, "activation": "relu", "dropout_rate": 0.0}], ["timexer", {"d_model": 512, "num_heads": 8, "num_encoder_layers": 2, "dim_ff": 256, "dropout_rate": 0.05, "activation": "relu", "patch_len": 5, "use_instance_scale": true}], ["timemixer", {"d_model": 16, "num_heads": 8, "num_encoder_layers": 1, "dim_ff": 256, "dropout_rate": 0.05, "moving_avg": 25, "top_k": 5, "channel_independence": true, "decomposition_method": "moving_avg", "down_sampling_method": "avg", "down_sampling_window": 1, "down_sampling_layers": 1, "use_instance_scale": true}], ["stid", {"node_dim": 32, "embed_dim": 1024, "input_dim": 1, "num_layer": 1, "if_node": true}], ["stnorm", {"tnorm_bool": true, "snorm_bool": true, "channels": 16, "kernel_size": 2, "blocks": 1, "layers": 2}], ["magnet", {"label_window_size": 48, "conv2d_in_channels": 1, "residual_channels": 32, "conv_channels": 32, "skip_channels": 4, "end_channels": 128, "node_dim": 40, "tanhalpha": 3.0, "static_feat": null, "dilation_exponential": 1, "kernel_size": 7, "gcn_depth": 2, "gcn_true": false, "propalpha": 0.05, "layer_norm_affline": true, "buildA_true": true, "predefined_A": null, "dropout": 0.3}], ["gwn", {"out_dim": 1, "supports": null, "gcn_bool": true, "addaptadj": true, "aptinit": null, "in_dim": 1, "residual_channels": 32, "dilation_channels": 32, "skip_channels": 256, "end_channels": 512, "kernel_size": 2, "blocks": 1, "layers": 2, "dropout": 0.3}], ["fgnn", {"embed_size": 128, "hidden_size": 256, "hard_thresholding_fraction": 1, "hidden_size_factor": 1, "sparsity_threshold": 0.01}], ["coat", {"mode": "dr", "activation": "linear", "use_instance_scale": false, "dropout_rate": 0.0}], ["gain", {"gat_hidden_size": 64, "gat_nhead": 128, "gru_hidden_size": 8, "gru_num_layers": 1, "cnn_kernel_size": 3, "cnn_out_channels": 16, "highway_window_size": 10, "dropout_rate": 0.5}]]}}}}