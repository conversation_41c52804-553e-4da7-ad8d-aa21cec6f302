{"global": {"seed": 2025, "device": "cpu", "gpu_ids": null, "loss": "MSE", "metrics": ["MSE", "MAE", "RMSE"], "scale": null, "log_root": "{root}/logs"}, "ETTh1": {"meta": {"frequency": "1 hour", "start": "2022-01-01 00:00:00", "end": "2023-01-01 00:00:00", "length": 17420, "time": "Date", "univariate": ["OT"], "multivariate": ["HUFL", "HULL", "MUFL", "MULL", "LUFL", "LULL", "OT"], "path": "{root}/Github_ETT_small/ETTh1.csv"}, "experiments": {"U_L48_H24": {"targets": "univariate", "split": {"input_window_size": 48, "output_window_size": 24, "horizon": 1, "stride": 1, "train_ratio": 0.8}, "trainer": {"optimizer": {"lr": 0.0005, "weight_decay": 0.0}, "lr_scheduler": {"step_size": 10, "gamma": 0.996}, "epoch_range": [1, 500], "early_stop": {"patience": 7, "delta": 0.01, "mode": "rel"}, "batch_size": 32, "shuffle": true, "verbose": 2}, "models": [["ed", {"rnn_cls": "gru", "hidden_size": 32, "num_layers": 1, "bidirectional": false, "dropout_rate": 0.0, "decoder_way": "mapping"}], ["ed", {"rnn_cls": "gru", "hidden_size": 64, "num_layers": 1, "bidirectional": false, "dropout_rate": 0.0, "decoder_way": "mapping"}], ["ed", {"rnn_cls": "gru", "hidden_size": 128, "num_layers": 1, "bidirectional": false, "dropout_rate": 0.0, "decoder_way": "mapping"}], ["ed", {"rnn_cls": "gru", "hidden_size": 256, "num_layers": 1, "bidirectional": false, "dropout_rate": 0.0, "decoder_way": "mapping"}]], "others": [["gar", {"activation": "linear"}], ["ann", {"hidden_size": 512}], ["drn", {"hidden_size": 128, "number_stacks": 8, "number_blocks_per_stack": 1, "use_rnn": false}], ["rnn", {"rnn_cls": "rnn", "hidden_size": 4, "num_layers": 1, "bidirectional": true, "dropout_rate": 0.0, "decoder_way": "mapping"}], ["rnn", {"rnn_cls": "lstm", "hidden_size": 4, "num_layers": 1, "bidirectional": true, "dropout_rate": 0.0, "decoder_way": "mapping"}], ["rnn", {"rnn_cls": "gru", "hidden_size": 4, "num_layers": 1, "bidirectional": true, "dropout_rate": 0.0, "decoder_way": "mapping"}], ["ed", {"rnn_cls": "gru", "hidden_size": 4, "num_layers": 1, "bidirectional": true, "dropout_rate": 0.0, "decoder_way": "mapping"}], ["cnn1d", {"out_channels": 128, "kernel_size": 13}], ["cnnrnn", {"cnn_out_channels": 128, "cnn_kernel_size": 13, "rnn_cls": "gru", "rnn_hidden_size": 4, "rnn_num_layers": 1, "rnn_bidirectional": true, "dropout_rate": 0.0, "decoder_way": "mapping"}, {"lr": 0.0005}], ["lstnet", {"cnn_out_channels": 50, "cnn_kernel_size": 9, "rnn_hidden_size": 50, "rnn_num_layers": 1, "skip_window_size": 24, "skip_gru_hidden_size": 20, "highway_window_size": 24, "dropout_rate": 0.0}], ["nhits", {"n_blocks": [1, 1, 1], "n_layers": [2, 2, 2, 2, 2, 2, 2, 2], "hidden_size": [[512, 512], [512, 512], [512, 512]], "pooling_sizes": [8, 8, 8], "downsample_frequencies": [24, 12, 1], "pooling_mode": "max", "interpolation_mode": "linear", "dropout": 0.0, "activation": "ReLU", "initialization": "lecun_normal", "batch_normalization": false, "shared_weights": false, "naive_level": true}], ["nlinear", {"individual": false}], ["dlinear", {"individual": false, "kernel_size": 5}], ["transformer", {"label_window_size": 48, "d_model": 512, "num_heads": 8, "num_encoder_layers": 2, "num_decoder_layers": 1, "dim_ff": 2048, "dropout_rate": 0.05}], ["informer", {"label_window_size": 48, "d_model": 512, "num_heads": 8, "num_encoder_layers  ": 2, "num_decoder_layers": 1, "dim_ff": 2048, "dropout_rate": 0.05}], ["autoformer", {"label_window_size": 48, "d_model": 512, "num_heads": 8, "num_encoder_layers": 2, "num_decoder_layers": 1, "dim_ff": 2048, "dropout_rate": 0.0, "moving_avg": 7}], ["fedformer", {"label_window_size": 48, "d_model": 512, "num_heads": 8, "num_encoder_layers": 2, "num_decoder_layers": 1, "dim_ff": 2048, "activation": "relu", "moving_avg": 7, "dropout_rate": 0.05, "version": "fourier", "mode_select": "random", "modes": 32}], ["film", {"d_model": 512, "use_instance_scale": true}], ["triformer", {"channels": 32, "patch_sizes": [8, 3], "mem_dim": 5}], ["crossformer", {"d_model": 512, "num_heads": 8, "num_encoder_layers": 1, "num_decoder_layers": 1, "dim_ff": 2048, "dropout_rate": 0.05, "factor": 5, "seg_len": 24, "win_size": 2}], ["patchtst", {"d_model": 512, "num_heads": 8, "num_encoder_layers": 6, "dim_ff": 2048, "patch_len": 24, "patch_stride": 12, "patch_padding": 0, "use_instance_scale": true}], ["s<PERSON><PERSON><PERSON><PERSON>", {"input_dim": 1, "output_dim": 1, "input_embedding_dim": 24, "tod_steps_per_day": 24, "tod_embedding_dim": 0, "dow_embedding_dim": 0, "spatial_embedding_dim": 0, "adaptive_embedding_dim": 80, "dim_ff": 2048, "num_heads": 8, "num_layers": 2, "dropout_rate": 0.05, "use_mixed_proj": true}], ["itransformer", {"d_model": 512, "num_heads": 8, "num_encoder_layers": 2, "dim_ff": 2048, "activation": "relu", "dropout_rate": 0.05, "use_instance_scale": true}], ["timexer", {"d_model": 512, "num_heads": 8, "num_encoder_layers": 2, "dim_ff": 2048, "dropout_rate": 0.05, "activation": "relu", "patch_len": 16, "use_instance_scale": true}], ["timemixer", {"d_model": 512, "num_heads": 8, "num_encoder_layers": 1, "dim_ff": 2048, "dropout_rate": 0.05, "moving_avg": 25, "top_k": 5, "channel_independence": true, "decomposition_method": "moving_avg", "down_sampling_method": "avg", "down_sampling_window": 1, "down_sampling_layers": 1, "use_instance_scale": true}], ["coat", {"mode": "dr", "activation": "linear", "use_instance_scale": false, "dropout_rate": 0.05}], ["tcoat", {"rnn_hidden_size": 16, "rnn_num_layers": 1, "rnn_bidirectional": false, "residual_window_size": 24, "residual_ratio": 1.0, "dropout_rate": 0.0}], ["codr", {"horizon": 1, "hidden_size": 64, "use_window_fluctuation_extraction": true, "dropout_rate": 0.2}]]}, "U_L96_H48": {"targets": "univariate", "split": {"input_window_size": 96, "output_window_size": 48, "horizon": 1, "stride": 1, "train_ratio": 0.8}, "trainer": {"optimizer": {"lr": 0.0002, "weight_decay": 0.0}, "lr_scheduler": {"step_size": 10, "gamma": 0.5}, "epoch_range": [1, 500], "early_stop": {"patience": 7, "delta": 0.01, "mode": "rel"}, "batch_size": 32, "shuffle": true}, "models": [["gar", {"activation": "linear"}, {"lr": 0.0002, "batch_size": 32, "shuffle": false}], ["ar", {"activation": "linear"}], ["var", {"activation": "linear"}], ["ann", {"hidden_size": 512}], ["drn", {"hidden_size": 64, "number_stacks": 7, "number_blocks_per_stack": 1, "use_rnn": false}], ["rnn", {"rnn_cls": "rnn", "hidden_size": 16, "num_layers": 1, "bidirectional": false, "dropout_rate": 0.0, "decoder_way": "mapping"}], ["rnn", {"rnn_cls": "lstm", "hidden_size": 16, "num_layers": 1, "bidirectional": false, "dropout_rate": 0.0, "decoder_way": "mapping"}], ["rnn", {"rnn_cls": "gru", "hidden_size": 16, "num_layers": 1, "bidirectional": false, "dropout_rate": 0.0, "decoder_way": "mapping"}], ["ed", {"rnn_cls": "gru", "hidden_size": 32, "num_layers": 1, "bidirectional": false, "dropout_rate": 0.0, "decoder_way": "mapping"}], ["cnn1d", {"out_channels": 1, "kernel_size": 1}], ["tcn", {"num_channels": [32], "kernel_size": 2, "dropout_rate": 0.2}], ["cnnrnn", {"cnn_out_channels": 50, "cnn_kernel_size": 9, "rnn_cls": "gru", "rnn_hidden_size": 32, "rnn_num_layers": 1, "rnn_bidirectional": false, "dropout_rate": 0.0, "decoder_way": "mapping"}], ["cnnrnnres", {"cnn_out_channels": 50, "cnn_kernel_size": 9, "rnn_cls": "gru", "rnn_hidden_size": 32, "rnn_num_layers": 1, "rnn_bidirectional": false, "dropout_rate": 0.0, "decoder_way": "mapping", "residual_window_size": 5, "residual_ratio": 0.1}], ["lstnet", {"cnn_out_channels": 50, "cnn_kernel_size": 9, "rnn_hidden_size": 50, "rnn_num_layers": 1, "skip_window_size": 24, "skip_gru_hidden_size": 20, "highway_window_size": 24, "dropout_rate": 0.0}], ["nhits", {"n_blocks": [1, 1, 1], "n_layers": [2, 2, 2, 2, 2, 2, 2, 2], "hidden_size": [[512, 512], [512, 512], [512, 512]], "pooling_sizes": [8, 8, 8], "downsample_frequencies": [24, 12, 1], "pooling_mode": "max", "interpolation_mode": "linear", "dropout": 0.0, "activation": "ReLU", "initialization": "lecun_normal", "batch_normalization": false, "shared_weights": false, "naive_level": true}], ["nlinear", {"individual": false}], ["dlinear", {"individual": false, "kernel_size": 5}], ["transformer", {"label_window_size": 48, "d_model": 512, "num_heads": 8, "num_encoder_layers": 2, "num_decoder_layers": 1, "dim_ff": 2048, "dropout_rate": 0.05}], ["informer", {"label_window_size": 48, "d_model": 512, "num_heads": 8, "num_encoder_layers": 2, "num_decoder_layers": 1, "dim_ff": 2048, "dropout_rate": 0.05}], ["autoformer", {"label_window_size": 48, "d_model": 512, "num_heads": 8, "num_encoder_layers": 2, "num_decoder_layers": 1, "dim_ff": 2048, "dropout_rate": 0.0, "moving_avg": 7}], ["fedformer", {"label_window_size": 48, "d_model": 512, "num_heads": 8, "num_encoder_layers": 2, "num_decoder_layers": 1, "dim_ff": 2048, "activation": "relu", "moving_avg": 7, "dropout_rate": 0.05, "version": "fourier", "mode_select": "random", "modes": 32}], ["film", {"d_model": 512, "use_instance_scale": true}], ["triformer", {"channels": 32, "patch_sizes": [8, 3], "mem_dim": 5}], ["crossformer", {"d_model": 512, "num_heads": 8, "num_encoder_layers": 1, "num_decoder_layers": 1, "dim_ff": 2048, "dropout_rate": 0.05, "factor": 5, "seg_len": 24, "win_size": 2}], ["timesnet", {"d_model": 512, "num_encoder_layers": 2, "dim_ff": 2048, "dropout_rate": 0.05, "num_kernels": 6, "top_k": 5, "use_instance_scale": false}], ["patchtst", {"d_model": 512, "num_heads": 8, "num_encoder_layers": 6, "dim_ff": 2048, "patch_len": 24, "patch_stride": 12, "patch_padding": 0, "use_instance_scale": true}], ["s<PERSON><PERSON><PERSON><PERSON>", {"input_dim": 1, "output_dim": 1, "input_embedding_dim": 24, "tod_steps_per_day": 24, "tod_embedding_dim": 0, "dow_embedding_dim": 0, "spatial_embedding_dim": 0, "adaptive_embedding_dim": 80, "dim_ff": 2048, "num_heads": 8, "num_layers": 2, "dropout_rate": 0.05, "use_mixed_proj": true}], ["itransformer", {"d_model": 512, "num_heads": 8, "num_encoder_layers": 2, "dim_ff": 2048, "activation": "relu", "dropout_rate": 0.05, "use_instance_scale": true}], ["timesfm", {"d_model": 512, "num_heads": 2, "num_layers": 2, "dim_ff": 2048, "dropout_rate": 0.05}], ["timer", {"patch_len": 4, "d_model": 512, "num_heads": 8, "e_layers": 1, "dim_ff": 2048, "activation": "relu", "dropout_rate": 0.05}], ["timexer", {"d_model": 512, "num_heads": 8, "num_encoder_layers": 2, "dim_ff": 2048, "dropout_rate": 0.05, "activation": "relu", "patch_len": 16, "use_instance_scale": true}], ["timemixer", {"d_model": 512, "num_heads": 8, "num_encoder_layers": 1, "dim_ff": 2048, "dropout_rate": 0.05, "moving_avg": 25, "top_k": 5, "channel_independence": true, "decomposition_method": "moving_avg", "down_sampling_method": "avg", "down_sampling_window": 1, "down_sampling_layers": 1, "use_instance_scale": true}], ["coat", {"mode": "dr", "activation": "linear", "use_instance_scale": false, "dropout_rate": 0.05}], ["tcoat", {"rnn_hidden_size": 16, "rnn_num_layers": 1, "rnn_bidirectional": false, "residual_window_size": 24, "residual_ratio": 1.0, "dropout_rate": 0.0}], ["codr", {"horizon": 1, "hidden_size": 64, "use_window_fluctuation_extraction": true, "dropout_rate": 0.2}]]}}}, "ETTh2": {"meta": {"frequency": "1 hour", "start": "2022-01-01 00:00:00", "end": "2023-01-01 00:00:00", "length": 17420, "time": "Date", "univariate": ["OT"], "multivariate": ["HUFL", "HULL", "MUFL", "MULL", "LUFL", "LULL", "OT"], "path": "{root}/Github_ETT_small/ETTh2.csv"}, "experiments": {"U_L48_H24": {"targets": "univariate", "split": {"input_window_size": 48, "output_window_size": 24, "horizon": 1, "stride": 1, "train_ratio": 0.8}, "trainer": {"optimizer": {"lr": 0.0002, "weight_decay": 0.0}, "lr_scheduler": {"step_size": 10, "gamma": 0.5}, "epoch_range": [1, 1], "early_stop": {"patience": 7, "delta": 0.01, "mode": "rel"}, "batch_size": 32, "shuffle": true}, "models": [["gar", {"activation": "linear"}, {"lr": 0.0002}], ["ar", {"activation": "linear"}], ["var", {"activation": "linear"}], ["ann", {"hidden_size": 512}], ["drn", {"hidden_size": 64, "number_stacks": 7, "number_blocks_per_stack": 1, "use_rnn": false}], ["rnn", {"rnn_cls": "rnn", "hidden_size": 16, "num_layers": 1, "bidirectional": false, "dropout_rate": 0.0, "decoder_way": "mapping"}], ["lstm", {"rnn_cls": "lstm", "hidden_size": 16, "num_layers": 1, "bidirectional": false, "dropout_rate": 0.0, "decoder_way": "mapping"}], ["gru", {"rnn_cls": "gru", "hidden_size": 16, "num_layers": 1, "bidirectional": false, "dropout_rate": 0.0, "decoder_way": "mapping"}], ["ed", {"rnn_cls": "gru", "hidden_size": 32, "num_layers": 1, "bidirectional": false, "dropout_rate": 0.0, "decoder_way": "mapping"}], ["cnn1d", {"out_channels": 1, "kernel_size": 1}], ["tcn", {"num_channels": [32], "kernel_size": 2, "dropout_rate": 0.2}], ["cnnrnn", {"cnn_out_channels": 50, "cnn_kernel_size": 9, "rnn_cls": "gru", "rnn_hidden_size": 32, "rnn_num_layers": 1, "rnn_bidirectional": false, "dropout_rate": 0.0, "decoder_way": "mapping"}], ["cnnrnnres", {"cnn_out_channels": 50, "cnn_kernel_size": 9, "rnn_cls": "gru", "rnn_hidden_size": 32, "rnn_num_layers": 1, "rnn_bidirectional": false, "dropout_rate": 0.0, "decoder_way": "mapping", "residual_window_size": 5, "residual_ratio": 0.1}], ["lstnet", {"cnn_out_channels": 50, "cnn_kernel_size": 9, "rnn_hidden_size": 50, "rnn_num_layers": 1, "skip_window_size": 24, "skip_gru_hidden_size": 20, "highway_window_size": 24, "dropout_rate": 0.0}], ["nhits", {"n_blocks": [1, 1, 1], "n_layers": [2, 2, 2, 2, 2, 2, 2, 2], "hidden_size": [[512, 512], [512, 512], [512, 512]], "pooling_sizes": [8, 8, 8], "downsample_frequencies": [24, 12, 1], "pooling_mode": "max", "interpolation_mode": "linear", "dropout": 0.0, "activation": "ReLU", "initialization": "lecun_normal", "batch_normalization": false, "shared_weights": false, "naive_level": true}], ["nlinear", {"individual": false}], ["dlinear", {"individual": false, "kernel_size": 5}], ["transformer", {"label_window_size": 48, "d_model": 512, "num_heads": 8, "num_encoder_layers": 2, "num_decoder_layers": 1, "dim_ff": 2048, "dropout_rate": 0.05}], ["informer", {"label_window_size": 48, "d_model": 512, "num_heads": 8, "num_encoder_layers": 2, "num_decoder_layers": 1, "dim_ff": 2048, "dropout_rate": 0.05}], ["autoformer", {"label_window_size": 48, "d_model": 512, "num_heads": 8, "num_encoder_layers": 2, "num_decoder_layers": 1, "dim_ff": 2048, "dropout_rate": 0.0, "moving_avg": 7}], ["fedformer", {"label_window_size": 48, "d_model": 512, "num_heads": 8, "num_encoder_layers": 2, "num_decoder_layers": 1, "dim_ff": 2048, "activation": "relu", "moving_avg": 7, "dropout_rate": 0.05, "version": "fourier", "mode_select": "random", "modes": 32}], ["film", {"d_model": 512, "use_instance_scale": true}], ["triformer", {"channels": 32, "patch_sizes": [8, 3], "mem_dim": 5}], ["crossformer", {"d_model": 512, "num_heads": 8, "num_encoder_layers": 1, "num_decoder_layers": 1, "dim_ff": 2048, "dropout_rate": 0.05, "factor": 5, "seg_len": 24, "win_size": 2}], ["timesnet", {"d_model": 512, "num_encoder_layers": 1, "dim_ff": 2048, "dropout_rate": 0.05, "num_kernels": 6, "top_k": 5, "use_instance_scale": false}], ["patchtst", {"d_model": 512, "num_heads": 8, "num_encoder_layers": 6, "dim_ff": 2048, "patch_len": 24, "patch_stride": 12, "patch_padding": 0, "use_instance_scale": true}], ["s<PERSON><PERSON><PERSON><PERSON>", {"input_dim": 1, "output_dim": 1, "input_embedding_dim": 24, "tod_steps_per_day": 24, "tod_embedding_dim": 0, "dow_embedding_dim": 0, "spatial_embedding_dim": 0, "adaptive_embedding_dim": 80, "dim_ff": 2048, "num_heads": 8, "num_layers": 2, "dropout_rate": 0.05, "use_mixed_proj": true}], ["itransformer", {"d_model": 512, "num_heads": 8, "num_encoder_layers": 2, "dim_ff": 2048, "activation": "relu", "dropout_rate": 0.05, "use_instance_scale": true}], ["timesfm", {"d_model": 512, "num_heads": 2, "num_layers": 2, "dim_ff": 2048, "dropout_rate": 0.05}], ["timer", {"patch_len": 4, "d_model": 512, "num_heads": 8, "e_layers": 1, "dim_ff": 2048, "activation": "relu", "dropout_rate": 0.05}], ["timexer", {"d_model": 512, "num_heads": 8, "num_encoder_layers": 2, "dim_ff": 2048, "dropout_rate": 0.05, "activation": "relu", "patch_len": 16, "use_instance_scale": true}], ["timemixer", {"d_model": 512, "num_heads": 8, "num_encoder_layers": 1, "dim_ff": 2048, "dropout_rate": 0.05, "moving_avg": 25, "top_k": 5, "channel_independence": true, "decomposition_method": "moving_avg", "down_sampling_method": "avg", "down_sampling_window": 1, "down_sampling_layers": 1, "use_instance_scale": true}], ["coat", {"mode": "dr", "activation": "linear", "use_instance_scale": false, "dropout_rate": 0.05}], ["tcoat", {"rnn_hidden_size": 16, "rnn_num_layers": 1, "rnn_bidirectional": false, "residual_window_size": 24, "residual_ratio": 1.0, "dropout_rate": 0.0}], ["codr", {"horizon": 1, "hidden_size": 64, "use_window_fluctuation_extraction": true, "dropout_rate": 0.2}]]}, "U_L96_H48": {"targets": "univariate", "split": {"input_window_size": 96, "output_window_size": 48, "horizon": 1, "stride": 1, "train_ratio": 0.8}, "trainer": {"optimizer": {"lr": 0.0002, "weight_decay": 0.0}, "lr_scheduler": {"step_size": 10, "gamma": 0.5}, "epoch_range": [1, 500], "early_stop": {"patience": 7, "delta": 0.01, "mode": "rel"}, "batch_size": 32, "shuffle": true}, "models": [["gar", {"activation": "linear"}, {"lr": 0.0002}], ["ar", {"activation": "linear"}], ["var", {"activation": "linear"}], ["ann", {"hidden_size": 512}], ["drn", {"hidden_size": 64, "number_stacks": 7, "number_blocks_per_stack": 1, "use_rnn": false}], ["rnn", {"rnn_cls": "rnn", "hidden_size": 16, "num_layers": 1, "bidirectional": false, "dropout_rate": 0.0, "decoder_way": "mapping"}], ["lstm", {"rnn_cls": "lstm", "hidden_size": 16, "num_layers": 1, "bidirectional": false, "dropout_rate": 0.0, "decoder_way": "mapping"}], ["gru", {"rnn_cls": "gru", "hidden_size": 16, "num_layers": 1, "bidirectional": false, "dropout_rate": 0.0, "decoder_way": "mapping"}], ["ed", {"rnn_cls": "gru", "hidden_size": 32, "num_layers": 1, "bidirectional": false, "dropout_rate": 0.0, "decoder_way": "mapping"}], ["cnn1d", {"out_channels": 1, "kernel_size": 1}], ["tcn", {"num_channels": [32], "kernel_size": 2, "dropout_rate": 0.2}], ["cnnrnn", {"cnn_out_channels": 50, "cnn_kernel_size": 9, "rnn_cls": "gru", "rnn_hidden_size": 32, "rnn_num_layers": 1, "rnn_bidirectional": false, "dropout_rate": 0.0, "decoder_way": "mapping"}], ["cnnrnnres", {"cnn_out_channels": 50, "cnn_kernel_size": 9, "rnn_cls": "gru", "rnn_hidden_size": 32, "rnn_num_layers": 1, "rnn_bidirectional": false, "dropout_rate": 0.0, "decoder_way": "mapping", "residual_window_size": 5, "residual_ratio": 0.1}], ["lstnet", {"cnn_out_channels": 50, "cnn_kernel_size": 9, "rnn_hidden_size": 50, "rnn_num_layers": 1, "skip_window_size": 24, "skip_gru_hidden_size": 20, "highway_window_size": 24, "dropout_rate": 0.0}], ["nhits", {"n_blocks": [1, 1, 1], "n_layers": [2, 2, 2, 2, 2, 2, 2, 2], "hidden_size": [[512, 512], [512, 512], [512, 512]], "pooling_sizes": [8, 8, 8], "downsample_frequencies": [24, 12, 1], "pooling_mode": "max", "interpolation_mode": "linear", "dropout": 0.0, "activation": "ReLU", "initialization": "lecun_normal", "batch_normalization": false, "shared_weights": false, "naive_level": true}], ["nlinear", {"individual": false}], ["dlinear", {"individual": false, "kernel_size": 5}], ["transformer", {"label_window_size": 48, "d_model": 512, "num_heads": 8, "num_encoder_layers": 2, "num_decoder_layers": 1, "dim_ff": 2048, "dropout_rate": 0.05}], ["informer", {"label_window_size": 48, "d_model": 512, "num_heads": 8, "num_encoder_layers": 2, "num_decoder_layers": 1, "dim_ff": 2048, "dropout_rate": 0.05}], ["autoformer", {"label_window_size": 48, "d_model": 512, "num_heads": 8, "num_encoder_layers": 2, "num_decoder_layers": 1, "dim_ff": 2048, "dropout_rate": 0.0, "moving_avg": 7}], ["fedformer", {"label_window_size": 48, "d_model": 512, "num_heads": 8, "num_encoder_layers": 2, "num_decoder_layers": 1, "dim_ff": 2048, "activation": "relu", "moving_avg": 7, "dropout_rate": 0.05, "version": "fourier", "mode_select": "random", "modes": 32}], ["film", {"d_model": 512, "use_instance_scale": true}], ["triformer", {"channels": 32, "patch_sizes": [8, 3], "mem_dim": 5}], ["crossformer", {"d_model": 512, "num_heads": 8, "num_encoder_layers": 1, "num_decoder_layers": 1, "dim_ff": 2048, "dropout_rate": 0.05, "factor": 5, "seg_len": 24, "win_size": 2}], ["timesnet", {"d_model": 512, "num_encoder_layers": 2, "dim_ff": 2048, "dropout_rate": 0.05, "num_kernels": 6, "top_k": 5, "use_instance_scale": false}], ["patchtst", {"d_model": 512, "num_heads": 8, "num_encoder_layers": 6, "dim_ff": 2048, "patch_len": 24, "patch_stride": 12, "patch_padding": 0, "use_instance_scale": true}], ["s<PERSON><PERSON><PERSON><PERSON>", {"input_dim": 1, "output_dim": 1, "input_embedding_dim": 24, "tod_steps_per_day": 24, "tod_embedding_dim": 0, "dow_embedding_dim": 0, "spatial_embedding_dim": 0, "adaptive_embedding_dim": 80, "dim_ff": 2048, "num_heads": 8, "num_layers": 2, "dropout_rate": 0.05, "use_mixed_proj": true}], ["itransformer", {"d_model": 512, "num_heads": 8, "num_encoder_layers": 2, "dim_ff": 2048, "activation": "relu", "dropout_rate": 0.05, "use_instance_scale": true}], ["timesfm", {"d_model": 512, "num_heads": 2, "num_layers": 2, "dim_ff": 2048, "dropout_rate": 0.05}], ["timer", {"patch_len": 4, "d_model": 512, "num_heads": 8, "e_layers": 1, "dim_ff": 2048, "activation": "relu", "dropout_rate": 0.05}], ["timexer", {"d_model": 512, "num_heads": 8, "num_encoder_layers": 2, "dim_ff": 2048, "dropout_rate": 0.05, "activation": "relu", "patch_len": 16, "use_instance_scale": true}], ["timemixer", {"d_model": 512, "num_heads": 8, "num_encoder_layers": 1, "dim_ff": 2048, "dropout_rate": 0.05, "moving_avg": 25, "top_k": 5, "channel_independence": true, "decomposition_method": "moving_avg", "down_sampling_method": "avg", "down_sampling_window": 1, "down_sampling_layers": 1, "use_instance_scale": true}], ["coat", {"mode": "dr", "activation": "linear", "use_instance_scale": false, "dropout_rate": 0.05}], ["tcoat", {"rnn_hidden_size": 16, "rnn_num_layers": 1, "rnn_bidirectional": false, "residual_window_size": 24, "residual_ratio": 1.0, "dropout_rate": 0.0}], ["codr", {"horizon": 1, "hidden_size": 64, "use_window_fluctuation_extraction": true, "dropout_rate": 0.2}]]}}}}