# Changelog

All notable changes to the Cabin wind power forecasting framework will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [1.0.0] - 2024-01-XX

### Added

#### Core Model
- **Cabin Model**: Main collaborative and adaptive framework for wind power forecasting
- **Three Architecture Variants**:
  - `only_target`: Uses exclusively historical wind power data
  - `data_first`: Early fusion of power and ambient data
  - `learning_first`: Separate feature learning followed by fusion
- **Ambient Representation Module (ARM)**: Multi-dimensional feature extraction with configurable softmax operations
- **Collaboration of Ambient Variables (CAV)**: Feature integration using GAR and KAN

#### Base Components
- **Global Autoregression (GAR)**: Temporal modeling component extracted from pyFAST
- **Kolmogorov-Arnold Networks (KAN)**: Advanced non-linear function approximation
- **Activation Functions**: Comprehensive activation function utilities including custom Sin activation

#### Data Processing
- **Data Preprocessing**: Normalization, denormalization, and window creation utilities
- **Dataset Classes**: PyTorch Dataset and DataLoader integration
- **Sample Data Generation**: Synthetic wind power and ambient data for testing
- **Data Quality Checks**: Missing data detection and handling

#### Evaluation
- **Comprehensive Metrics**: MSE, MAE, RMSE, CV-RMSE, R², MAPE, NRMSE
- **Model Evaluation**: Automated evaluation pipeline with batch processing
- **Early Stopping**: Training utility with best weight restoration
- **Visualization**: Prediction plotting and analysis tools

#### Examples and Documentation
- **Basic Usage Example**: Simple model initialization and prediction
- **Training Example**: Complete training pipeline with multiple configurations
- **Evaluation Example**: Comprehensive model analysis and comparison
- **Data Preparation Example**: Data loading, preprocessing, and quality checks
- **Comprehensive README**: Installation, usage, and API documentation
- **Contributing Guidelines**: Development setup and contribution process

#### Package Infrastructure
- **PyPI Package**: Complete setup.py with dependencies and metadata
- **Type Hints**: Full type annotation support
- **Modular Design**: Clean separation of concerns with importable components
- **CLI Tools**: Command-line interfaces for training and prediction (planned)

### Features

#### Model Capabilities
- **Multi-variate Forecasting**: Support for multiple wind turbines and ambient variables
- **Flexible Window Sizes**: Configurable input and output sequence lengths
- **Adaptive Architecture**: Automatic adaptation to data availability scenarios
- **GPU Support**: CUDA acceleration for training and inference
- **Batch Processing**: Efficient batch-wise training and evaluation

#### Advanced Components
- **Configurable ARM**: Three types of softmax normalization (sample, temporal, feature-wise)
- **KAN Integration**: Learnable spline functions for complex non-linear relationships
- **Regularization**: Built-in L2 regularization and gradient clipping
- **Learning Rate Scheduling**: Adaptive learning rate with plateau detection

#### Data Handling
- **Multiple Data Formats**: Support for NumPy arrays, pandas DataFrames, and CSV files
- **Chronological Splitting**: Time-aware train/validation/test splits
- **Missing Data Handling**: Multiple imputation strategies
- **Data Validation**: Comprehensive quality checks and outlier detection

### Technical Specifications

#### Requirements
- Python 3.8+
- PyTorch 1.9+
- NumPy 1.21+
- pandas 1.3+
- scikit-learn 1.0+

#### Performance
- **Memory Efficient**: Optimized tensor operations and gradient computation
- **Scalable**: Supports datasets from small experiments to large-scale deployments
- **Fast Training**: Efficient implementation with optional GPU acceleration

#### Compatibility
- **Cross-platform**: Windows, macOS, and Linux support
- **Python Versions**: Compatible with Python 3.8, 3.9, 3.10, 3.11
- **PyTorch Versions**: Compatible with PyTorch 1.9+ and 2.0+

### Documentation

#### API Documentation
- Complete docstring coverage for all public functions and classes
- Type hints for all function parameters and return values
- Usage examples in docstrings

#### Examples
- **4 Complete Examples**: From basic usage to advanced evaluation
- **Real-world Scenarios**: Practical examples with synthetic data
- **Best Practices**: Recommended workflows and configurations

#### Guides
- **Installation Guide**: Multiple installation methods
- **Quick Start**: Get up and running in minutes
- **Advanced Usage**: Configuration options and customization
- **Contributing Guide**: Development setup and guidelines

### Research Foundation

Based on the paper:
**"Cabin: A Collaborative and Adaptive Framework for Wind Power Forecasting Integrating Ambient Variables"**

- Authors: <AUTHORS>
- Demonstrated superior performance over 34 baseline models
- Significant improvements: up to 48.63% MSE reduction, 28.33% CV-RMSE reduction
- Evaluated on Turkey Wind Power Forecasting (TWPF) and Greece Wind Power Forecasting (GWPF) datasets

### License

Released under MIT License - see LICENSE file for details.

---

## Future Releases (Planned)

### [1.1.0] - Planned
- Additional ARM variants
- More KAN configurations
- Hyperparameter optimization utilities
- Model interpretability tools

### [1.2.0] - Planned
- Multi-site forecasting capabilities
- Probabilistic forecasting
- Online learning support
- Advanced visualization tools

### [2.0.0] - Planned
- Breaking API changes for improved usability
- Integration with popular ML frameworks
- Production deployment utilities
- Real-time forecasting capabilities
