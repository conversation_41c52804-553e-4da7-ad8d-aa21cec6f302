#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Installation validation script for Cabin package.

This script validates that the Cabin package is correctly installed and functional.
Run this after installation to ensure everything works properly.
"""

import sys
import traceback
import torch
import numpy as np


def test_imports():
    """Test that all modules can be imported."""
    print("Testing imports...")
    
    try:
        # Test main imports
        from cabin import Cabin
        from cabin.models.components import AmbientRepresentationModule, CollaborationAmbientVariables
        from cabin.models.base import GAR, KAN, get_activation_cls
        from cabin.utils import normalize_data, create_windows, calculate_metrics
        print("✓ All imports successful")
        return True
    except ImportError as e:
        print(f"✗ Import error: {e}")
        return False


def test_model_initialization():
    """Test model initialization."""
    print("\nTesting model initialization...")
    
    try:
        from cabin import Cabin
        
        # Test all three modes
        modes = ['only_target', 'data_first', 'learning_first']
        
        for mode in modes:
            if mode == 'only_target':
                model = Cabin(
                    input_window_size=10,
                    input_vars=1,
                    output_window_size=1,
                    output_vars=1,
                    mode=mode
                )
            else:
                model = Cabin(
                    input_window_size=10,
                    input_vars=1,
                    output_window_size=1,
                    output_vars=1,
                    ex_vars=3,
                    mode=mode
                )
            
            print(f"✓ {mode} mode initialized successfully")
        
        return True
    except Exception as e:
        print(f"✗ Model initialization error: {e}")
        traceback.print_exc()
        return False


def test_forward_pass():
    """Test model forward pass."""
    print("\nTesting forward pass...")
    
    try:
        from cabin import Cabin
        
        # Test only_target mode
        model = Cabin(
            input_window_size=5,
            input_vars=2,
            output_window_size=1,
            output_vars=2,
            mode='only_target'
        )
        
        x = torch.randn(3, 5, 2)
        output = model(x)
        
        assert output.shape == (3, 1, 2), f"Expected shape (3, 1, 2), got {output.shape}"
        assert not torch.isnan(output).any(), "Output contains NaN values"
        print("✓ only_target forward pass successful")
        
        # Test data_first mode
        model = Cabin(
            input_window_size=5,
            input_vars=1,
            output_window_size=1,
            output_vars=1,
            ex_vars=2,
            mode='data_first'
        )
        
        x = torch.randn(3, 5, 1)
        e = torch.randn(3, 5, 2)
        output = model(x, e)
        
        assert output.shape == (3, 1, 1), f"Expected shape (3, 1, 1), got {output.shape}"
        assert not torch.isnan(output).any(), "Output contains NaN values"
        print("✓ data_first forward pass successful")
        
        return True
    except Exception as e:
        print(f"✗ Forward pass error: {e}")
        traceback.print_exc()
        return False


def test_base_components():
    """Test base components (GAR, KAN)."""
    print("\nTesting base components...")
    
    try:
        from cabin.models.base import GAR, KAN
        
        # Test GAR
        gar = GAR(input_window_size=5, output_window_size=2)
        x = torch.randn(2, 5, 3)
        output = gar(x)
        
        assert output.shape == (2, 2, 3), f"GAR: Expected shape (2, 2, 3), got {output.shape}"
        print("✓ GAR component working")
        
        # Test KAN
        kan = KAN(
            input_window_size=3,
            input_vars=2,
            output_window_size=1,
            output_vars=1,
            layers_hidden=[4]
        )
        x = torch.randn(2, 3, 2)
        output = kan(x)
        
        assert output.shape == (2, 1, 1), f"KAN: Expected shape (2, 1, 1), got {output.shape}"
        print("✓ KAN component working")
        
        return True
    except Exception as e:
        print(f"✗ Base components error: {e}")
        traceback.print_exc()
        return False


def test_utilities():
    """Test utility functions."""
    print("\nTesting utilities...")
    
    try:
        from cabin.utils import normalize_data, create_windows, calculate_metrics, load_sample_data
        
        # Test data loading
        power_data, ambient_data = load_sample_data("synthetic")
        assert power_data.shape[0] == 365, f"Expected 365 days, got {power_data.shape[0]}"
        assert ambient_data.shape[1] == 4, f"Expected 4 ambient vars, got {ambient_data.shape[1]}"
        print("✓ Sample data loading working")
        
        # Test normalization
        normalized, scaler = normalize_data(power_data)
        assert normalized.min() >= 0 and normalized.max() <= 1, "Normalization failed"
        print("✓ Data normalization working")
        
        # Test window creation
        power_windows, ambient_windows, targets = create_windows(
            power_data, ambient_data, window_size=10, horizon=1
        )
        expected_windows = len(power_data) - 10 - 1 + 1
        assert len(power_windows) == expected_windows, f"Expected {expected_windows} windows, got {len(power_windows)}"
        print("✓ Window creation working")
        
        # Test metrics
        y_true = np.array([1.0, 2.0, 3.0, 4.0])
        y_pred = np.array([1.1, 1.9, 3.1, 3.9])
        metrics = calculate_metrics(y_true, y_pred)
        
        required_metrics = ['MSE', 'MAE', 'RMSE', 'R2']
        for metric in required_metrics:
            assert metric in metrics, f"Missing metric: {metric}"
        print("✓ Metrics calculation working")
        
        return True
    except Exception as e:
        print(f"✗ Utilities error: {e}")
        traceback.print_exc()
        return False


def test_training_pipeline():
    """Test a simple training pipeline."""
    print("\nTesting training pipeline...")
    
    try:
        from cabin import Cabin
        from cabin.utils import load_sample_data, prepare_data
        import torch.nn as nn
        import torch.optim as optim
        
        # Load data
        power_data, ambient_data = load_sample_data("synthetic")
        
        # Prepare data (small subset for quick test)
        train_loader, val_loader, test_loader = prepare_data(
            power_data=power_data[:100],  # Use only first 100 samples
            ambient_data=ambient_data[:100],
            window_size=5,
            horizon=1,
            batch_size=8
        )
        
        # Initialize model
        model = Cabin(
            input_window_size=5,
            input_vars=1,
            output_window_size=1,
            output_vars=1,
            ex_vars=4,
            hidden_size=16,  # Small for quick test
            mode='data_first'
        )
        
        # Quick training test (1 epoch)
        criterion = nn.MSELoss()
        optimizer = optim.Adam(model.parameters(), lr=0.01)
        
        model.train()
        for batch in train_loader:
            power_data, ambient_data, targets = batch
            
            optimizer.zero_grad()
            predictions = model(power_data, ambient_data)
            loss = criterion(predictions, targets)
            loss.backward()
            optimizer.step()
            
            break  # Just test one batch
        
        print("✓ Training pipeline working")
        return True
    except Exception as e:
        print(f"✗ Training pipeline error: {e}")
        traceback.print_exc()
        return False


def main():
    """Run all validation tests."""
    print("Cabin Package Validation")
    print("=" * 25)
    print(f"Python version: {sys.version}")
    print(f"PyTorch version: {torch.__version__}")
    print(f"NumPy version: {np.__version__}")
    
    tests = [
        test_imports,
        test_model_initialization,
        test_forward_pass,
        test_base_components,
        test_utilities,
        test_training_pipeline
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ Test failed with exception: {e}")
            traceback.print_exc()
    
    print(f"\nValidation Results")
    print("-" * 18)
    print(f"Passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 All tests passed! Cabin is ready to use.")
        return 0
    else:
        print("❌ Some tests failed. Please check the installation.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
