#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Paper reproduction example for Cabin model.

This script demonstrates how to reproduce results similar to those reported
in the Cabin paper, including:
1. Comparison with baseline models
2. Evaluation on multiple metrics
3. Analysis of different configurations
4. Performance visualization
"""

import torch
import torch.nn as nn
import numpy as np
import time
from typing import Dict, List
import matplotlib.pyplot as plt

from cabin import Cabin
from cabin.utils import (
    load_sample_data, prepare_data, evaluate_model,
    print_metrics, calculate_metrics
)


class SimpleBaseline(nn.Module):
    """Simple baseline models for comparison."""
    
    def __init__(self, input_size: int, output_size: int, model_type: str = 'linear'):
        super().__init__()
        self.model_type = model_type
        
        if model_type == 'linear':
            self.model = nn.Linear(input_size, output_size)
        elif model_type == 'mlp':
            self.model = nn.Sequential(
                nn.Linear(input_size, 64),
                nn.<PERSON><PERSON><PERSON>(),
                nn.<PERSON>ar(64, 32),
                nn.<PERSON><PERSON><PERSON>(),
                nn.Linear(32, output_size)
            )
        elif model_type == 'lstm':
            self.lstm = nn.LSTM(input_size // 10, 32, batch_first=True)  # Assuming window_size=10
            self.fc = nn.Linear(32, output_size)
        
    def forward(self, x, e=None):
        if self.model_type == 'lstm':
            # Reshape for LSTM: [batch, seq, features]
            batch_size = x.size(0)
            x = x.view(batch_size, 10, -1)  # Assuming window_size=10
            lstm_out, _ = self.lstm(x)
            output = self.fc(lstm_out[:, -1, :])  # Use last output
            return output.unsqueeze(1)  # Add time dimension
        else:
            # Flatten input for linear/MLP models
            x_flat = x.view(x.size(0), -1)
            if e is not None:
                e_flat = e.view(e.size(0), -1)
                x_flat = torch.cat([x_flat, e_flat], dim=1)
            
            output = self.model(x_flat)
            return output.unsqueeze(1)  # Add time dimension


def create_baseline_models(input_vars: int, output_vars: int, ex_vars: int, window_size: int) -> Dict[str, nn.Module]:
    """Create baseline models for comparison."""
    models = {}
    
    # Power-only models
    power_input_size = input_vars * window_size
    
    models['Linear'] = SimpleBaseline(power_input_size, output_vars, 'linear')
    models['MLP'] = SimpleBaseline(power_input_size, output_vars, 'mlp')
    models['LSTM'] = SimpleBaseline(input_vars, output_vars, 'lstm')
    
    # Models with ambient data
    combined_input_size = (input_vars + ex_vars) * window_size
    
    models['Linear+Ambient'] = SimpleBaseline(combined_input_size, output_vars, 'linear')
    models['MLP+Ambient'] = SimpleBaseline(combined_input_size, output_vars, 'mlp')
    
    return models


def train_baseline_model(model: nn.Module, train_loader, val_loader, 
                        num_epochs: int = 50, device: str = 'cpu') -> nn.Module:
    """Train a baseline model."""
    model.to(device)
    criterion = nn.MSELoss()
    optimizer = torch.optim.Adam(model.parameters(), lr=0.001)
    
    best_val_loss = float('inf')
    best_model_state = None
    patience_counter = 0
    patience = 10
    
    for epoch in range(num_epochs):
        # Training
        model.train()
        train_loss = 0.0
        
        for batch in train_loader:
            if len(batch) == 3:  # Power, ambient, targets
                power_data, ambient_data, targets = batch
                power_data = power_data.to(device)
                ambient_data = ambient_data.to(device)
                targets = targets.to(device)
                
                optimizer.zero_grad()
                predictions = model(power_data, ambient_data)
                loss = criterion(predictions, targets)
            else:  # Power, targets
                power_data, targets = batch
                power_data = power_data.to(device)
                targets = targets.to(device)
                
                optimizer.zero_grad()
                predictions = model(power_data)
                loss = criterion(predictions, targets)
            
            loss.backward()
            optimizer.step()
            train_loss += loss.item()
        
        # Validation
        model.eval()
        val_loss = 0.0
        with torch.no_grad():
            for batch in val_loader:
                if len(batch) == 3:
                    power_data, ambient_data, targets = batch
                    power_data = power_data.to(device)
                    ambient_data = ambient_data.to(device)
                    targets = targets.to(device)
                    predictions = model(power_data, ambient_data)
                else:
                    power_data, targets = batch
                    power_data = power_data.to(device)
                    targets = targets.to(device)
                    predictions = model(power_data)
                
                loss = criterion(predictions, targets)
                val_loss += loss.item()
        
        val_loss /= len(val_loader)
        
        # Early stopping
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            best_model_state = model.state_dict().copy()
            patience_counter = 0
        else:
            patience_counter += 1
            
        if patience_counter >= patience:
            break
    
    # Restore best model
    if best_model_state is not None:
        model.load_state_dict(best_model_state)
    
    return model


def run_comprehensive_evaluation():
    """Run comprehensive evaluation comparing Cabin with baselines."""
    print("Cabin Paper Reproduction - Comprehensive Evaluation")
    print("=" * 55)
    
    # Set random seeds
    torch.manual_seed(42)
    np.random.seed(42)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    # 1. Load and prepare data
    print("\n1. Loading and preparing data...")
    power_data, ambient_data = load_sample_data("synthetic")
    
    train_loader, val_loader, test_loader = prepare_data(
        power_data=power_data,
        ambient_data=ambient_data,
        window_size=10,
        horizon=1,
        batch_size=32,
        normalize=True
    )
    
    print(f"Data prepared: {len(train_loader)} train, {len(val_loader)} val, {len(test_loader)} test batches")
    
    # 2. Create and train Cabin models
    print("\n2. Training Cabin models...")
    cabin_models = {}
    cabin_configs = [
        ('Cabin-OnlyTarget', 'only_target', False),
        ('Cabin-DataFirst', 'data_first', True),
        ('Cabin-LearningFirst', 'learning_first', True)
    ]
    
    for name, mode, use_ambient in cabin_configs:
        print(f"Training {name}...")
        
        if use_ambient:
            model = Cabin(
                input_window_size=10,
                input_vars=1,
                output_window_size=1,
                output_vars=1,
                ex_vars=4,
                hidden_size=64,
                mode=mode
            )
        else:
            model = Cabin(
                input_window_size=10,
                input_vars=1,
                output_window_size=1,
                output_vars=1,
                hidden_size=64,
                mode=mode
            )
        
        # Quick training for demo (in practice, use more epochs)
        trained_model = train_baseline_model(model, train_loader, val_loader, 
                                           num_epochs=30, device=device)
        cabin_models[name] = trained_model
    
    # 3. Create and train baseline models
    print("\n3. Training baseline models...")
    baseline_models = create_baseline_models(
        input_vars=1, output_vars=1, ex_vars=4, window_size=10
    )
    
    trained_baselines = {}
    for name, model in baseline_models.items():
        print(f"Training {name}...")
        trained_model = train_baseline_model(model, train_loader, val_loader,
                                           num_epochs=30, device=device)
        trained_baselines[name] = trained_model
    
    # 4. Evaluate all models
    print("\n4. Evaluating all models...")
    results = {}
    
    # Evaluate Cabin models
    for name, model in cabin_models.items():
        metrics, _ = evaluate_model(model, test_loader, device=device)
        results[name] = metrics
        print(f"{name} - MSE: {metrics['MSE']:.6f}, R²: {metrics['R2']:.4f}")
    
    # Evaluate baseline models
    for name, model in trained_baselines.items():
        metrics, _ = evaluate_model(model, test_loader, device=device)
        results[name] = metrics
        print(f"{name} - MSE: {metrics['MSE']:.6f}, R²: {metrics['R2']:.4f}")
    
    # 5. Create comparison table
    print("\n5. Model Comparison Results")
    print("-" * 80)
    print(f"{'Model':<20} {'MSE':<12} {'MAE':<12} {'RMSE':<12} {'R²':<8} {'CV-RMSE':<10}")
    print("-" * 80)
    
    # Sort by MSE (lower is better)
    sorted_results = sorted(results.items(), key=lambda x: x[1]['MSE'])
    
    for name, metrics in sorted_results:
        print(f"{name:<20} {metrics['MSE']:<12.6f} {metrics['MAE']:<12.6f} "
              f"{metrics['RMSE']:<12.6f} {metrics['R2']:<8.4f} {metrics['CV_RMSE']:<10.6f}")
    
    # 6. Calculate improvements
    print("\n6. Performance Improvements")
    print("-" * 30)
    
    best_cabin = min(cabin_models.keys(), key=lambda x: results[x]['MSE'])
    best_baseline = min(trained_baselines.keys(), key=lambda x: results[x]['MSE'])
    
    cabin_mse = results[best_cabin]['MSE']
    baseline_mse = results[best_baseline]['MSE']
    
    mse_improvement = ((baseline_mse - cabin_mse) / baseline_mse) * 100
    
    print(f"Best Cabin model: {best_cabin}")
    print(f"Best baseline model: {best_baseline}")
    print(f"MSE improvement: {mse_improvement:.2f}%")
    
    cabin_cv_rmse = results[best_cabin]['CV_RMSE']
    baseline_cv_rmse = results[best_baseline]['CV_RMSE']
    cv_rmse_improvement = ((baseline_cv_rmse - cabin_cv_rmse) / baseline_cv_rmse) * 100
    
    print(f"CV-RMSE improvement: {cv_rmse_improvement:.2f}%")
    
    # 7. Visualize results
    print("\n7. Creating visualizations...")
    try:
        plot_comparison_results(results)
    except Exception as e:
        print(f"Visualization skipped: {e}")
    
    # 8. Model complexity analysis
    print("\n8. Model Complexity Analysis")
    print("-" * 30)
    
    all_models = {**cabin_models, **trained_baselines}
    
    for name, model in all_models.items():
        param_count = sum(p.numel() for p in model.parameters())
        trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
        
        print(f"{name:<20} Parameters: {param_count:>8,} (Trainable: {trainable_params:>8,})")
    
    return results


def plot_comparison_results(results: Dict[str, Dict[str, float]]):
    """Plot comparison results."""
    models = list(results.keys())
    mse_values = [results[model]['MSE'] for model in models]
    r2_values = [results[model]['R2'] for model in models]
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    # MSE comparison
    colors = ['red' if 'Cabin' in model else 'blue' for model in models]
    bars1 = ax1.bar(range(len(models)), mse_values, color=colors, alpha=0.7)
    ax1.set_xlabel('Models')
    ax1.set_ylabel('MSE (lower is better)')
    ax1.set_title('Mean Squared Error Comparison')
    ax1.set_xticks(range(len(models)))
    ax1.set_xticklabels(models, rotation=45, ha='right')
    ax1.grid(True, alpha=0.3)
    
    # Add value labels on bars
    for bar, value in zip(bars1, mse_values):
        ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(mse_values)*0.01,
                f'{value:.4f}', ha='center', va='bottom', fontsize=8)
    
    # R² comparison
    bars2 = ax2.bar(range(len(models)), r2_values, color=colors, alpha=0.7)
    ax2.set_xlabel('Models')
    ax2.set_ylabel('R² (higher is better)')
    ax2.set_title('R² Score Comparison')
    ax2.set_xticks(range(len(models)))
    ax2.set_xticklabels(models, rotation=45, ha='right')
    ax2.grid(True, alpha=0.3)
    
    # Add value labels on bars
    for bar, value in zip(bars2, r2_values):
        ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(r2_values)*0.01,
                f'{value:.3f}', ha='center', va='bottom', fontsize=8)
    
    plt.tight_layout()
    plt.show()


def main():
    """Main function to run paper reproduction."""
    start_time = time.time()
    
    results = run_comprehensive_evaluation()
    
    end_time = time.time()
    
    print(f"\n9. Summary")
    print("-" * 10)
    print(f"✓ Evaluated {len(results)} models")
    print(f"✓ Cabin models show competitive performance")
    print(f"✓ Evaluation completed in {end_time - start_time:.1f} seconds")
    print("\nNote: This is a demonstration with synthetic data and limited training.")
    print("For paper-quality results, use real wind power datasets and longer training.")
    
    # Best performing model
    best_model = min(results.keys(), key=lambda x: results[x]['MSE'])
    print(f"\nBest performing model: {best_model}")
    print_metrics(results[best_model], f"{best_model} Performance")


if __name__ == "__main__":
    main()
