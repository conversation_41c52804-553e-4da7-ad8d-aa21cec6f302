#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Examples package for Cabin wind power forecasting framework.

This package contains example scripts demonstrating various aspects of the Cabin model:
- basic_usage.py: Simple model initialization and prediction
- training_example.py: Complete training pipeline
- evaluation_example.py: Model evaluation and analysis
- data_preparation.py: Data preprocessing and quality checks
"""
