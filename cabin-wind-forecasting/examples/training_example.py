#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Complete training example for Cabin wind power forecasting model.

This script demonstrates:
1. Data preparation and preprocessing
2. Model initialization and training
3. Validation and early stopping
4. Model evaluation and metrics
"""

import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
from tqdm import tqdm

from cabin import Cabin
from cabin.utils import (
    load_sample_data, prepare_data, evaluate_model, 
    print_metrics, EarlyStopping
)


def train_model(model, train_loader, val_loader, num_epochs=100, 
                learning_rate=0.001, device='cpu', patience=10):
    """
    Train the Cabin model with early stopping.
    
    Args:
        model: Cabin model to train
        train_loader: Training data loader
        val_loader: Validation data loader
        num_epochs: Maximum number of epochs
        learning_rate: Learning rate for optimizer
        device: Device to train on
        patience: Early stopping patience
        
    Returns:
        Tuple of (trained_model, training_history)
    """
    model.to(device)
    criterion = nn.MSELoss()
    optimizer = optim.Adam(model.parameters(), lr=learning_rate, weight_decay=1e-5)
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=5, factor=0.5)
    early_stopping = EarlyStopping(patience=patience, restore_best_weights=True)
    
    history = {
        'train_loss': [],
        'val_loss': [],
        'learning_rate': []
    }
    
    print(f"Training on {device}")
    print(f"Model parameters: {sum(p.numel() for p in model.parameters()):,}")
    
    for epoch in range(num_epochs):
        # Training phase
        model.train()
        train_loss = 0.0
        train_batches = 0
        
        pbar = tqdm(train_loader, desc=f'Epoch {epoch+1}/{num_epochs}')
        for batch in pbar:
            if len(batch) == 3:  # Power, ambient, targets
                power_data, ambient_data, targets = batch
                power_data = power_data.to(device)
                ambient_data = ambient_data.to(device)
                targets = targets.to(device)
                
                optimizer.zero_grad()
                predictions = model(power_data, ambient_data)
                loss = criterion(predictions, targets)
                
            else:  # Power, targets (only-target mode)
                power_data, targets = batch
                power_data = power_data.to(device)
                targets = targets.to(device)
                
                optimizer.zero_grad()
                predictions = model(power_data)
                loss = criterion(predictions, targets)
            
            loss.backward()
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            optimizer.step()
            
            train_loss += loss.item()
            train_batches += 1
            
            pbar.set_postfix({'loss': f'{loss.item():.6f}'})
        
        avg_train_loss = train_loss / train_batches
        
        # Validation phase
        model.eval()
        val_loss = 0.0
        val_batches = 0
        
        with torch.no_grad():
            for batch in val_loader:
                if len(batch) == 3:  # Power, ambient, targets
                    power_data, ambient_data, targets = batch
                    power_data = power_data.to(device)
                    ambient_data = ambient_data.to(device)
                    targets = targets.to(device)
                    
                    predictions = model(power_data, ambient_data)
                    loss = criterion(predictions, targets)
                    
                else:  # Power, targets (only-target mode)
                    power_data, targets = batch
                    power_data = power_data.to(device)
                    targets = targets.to(device)
                    
                    predictions = model(power_data)
                    loss = criterion(predictions, targets)
                
                val_loss += loss.item()
                val_batches += 1
        
        avg_val_loss = val_loss / val_batches
        
        # Update learning rate scheduler
        scheduler.step(avg_val_loss)
        current_lr = optimizer.param_groups[0]['lr']
        
        # Store history
        history['train_loss'].append(avg_train_loss)
        history['val_loss'].append(avg_val_loss)
        history['learning_rate'].append(current_lr)
        
        print(f'Epoch {epoch+1}: Train Loss: {avg_train_loss:.6f}, '
              f'Val Loss: {avg_val_loss:.6f}, LR: {current_lr:.2e}')
        
        # Early stopping check
        if early_stopping(avg_val_loss, model):
            print(f'Early stopping at epoch {epoch+1}')
            break
    
    return model, history


def main():
    print("Cabin Wind Power Forecasting - Training Example")
    print("=" * 50)
    
    # Set random seeds for reproducibility
    torch.manual_seed(42)
    np.random.seed(42)
    
    # Check for GPU availability
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    # 1. Load and prepare data
    print("\n1. Loading and preparing data...")
    power_data, ambient_data = load_sample_data("synthetic")
    
    # Prepare data loaders
    train_loader, val_loader, test_loader = prepare_data(
        power_data=power_data,
        ambient_data=ambient_data,
        window_size=10,
        horizon=1,
        train_ratio=0.7,
        val_ratio=0.1,
        batch_size=16,
        normalize=True
    )
    
    print(f"Training batches: {len(train_loader)}")
    print(f"Validation batches: {len(val_loader)}")
    print(f"Test batches: {len(test_loader)}")
    
    # 2. Train different model configurations
    configurations = [
        {
            'name': 'Only-Target',
            'mode': 'only_target',
            'use_ambient': False
        },
        {
            'name': 'Data-First',
            'mode': 'data_first',
            'use_ambient': True
        },
        {
            'name': 'Learning-First',
            'mode': 'learning_first',
            'use_ambient': True
        }
    ]
    
    results = {}
    
    for config in configurations:
        print(f"\n2. Training {config['name']} Configuration")
        print("-" * 45)
        
        # Initialize model
        if config['use_ambient']:
            model = Cabin(
                input_window_size=10,
                input_vars=power_data.shape[1],
                output_window_size=1,
                output_vars=power_data.shape[1],
                ex_vars=ambient_data.shape[1],
                hidden_size=64,
                mode=config['mode'],
                use_activation=True
            )
        else:
            model = Cabin(
                input_window_size=10,
                input_vars=power_data.shape[1],
                output_window_size=1,
                output_vars=power_data.shape[1],
                hidden_size=64,
                mode=config['mode'],
                use_activation=True
            )
        
        # Train model
        trained_model, history = train_model(
            model=model,
            train_loader=train_loader,
            val_loader=val_loader,
            num_epochs=50,  # Reduced for demo
            learning_rate=0.001,
            device=device,
            patience=10
        )
        
        # Evaluate on test set
        print(f"\n3. Evaluating {config['name']} on test set...")
        test_metrics, test_loss = evaluate_model(
            trained_model, test_loader, device=device
        )
        
        print_metrics(test_metrics, f"{config['name']} Test Results")
        
        # Store results
        results[config['name']] = {
            'model': trained_model,
            'history': history,
            'test_metrics': test_metrics,
            'test_loss': test_loss
        }
    
    # 4. Compare results
    print(f"\n4. Model Comparison")
    print("-" * 20)
    print(f"{'Configuration':<15} {'MSE':<10} {'MAE':<10} {'R²':<8} {'CV-RMSE':<10}")
    print("-" * 60)
    
    for name, result in results.items():
        metrics = result['test_metrics']
        print(f"{name:<15} {metrics['MSE']:<10.6f} {metrics['MAE']:<10.6f} "
              f"{metrics['R2']:<8.4f} {metrics['CV_RMSE']:<10.6f}")
    
    # 5. Find best model
    best_model_name = min(results.keys(), 
                         key=lambda x: results[x]['test_metrics']['MSE'])
    best_model = results[best_model_name]['model']
    
    print(f"\nBest model: {best_model_name}")
    print(f"Best MSE: {results[best_model_name]['test_metrics']['MSE']:.6f}")
    
    # 6. Save best model
    print(f"\n5. Saving best model...")
    torch.save({
        'model_state_dict': best_model.state_dict(),
        'model_config': best_model.get_model_info(),
        'test_metrics': results[best_model_name]['test_metrics']
    }, 'best_cabin_model.pth')
    
    print("Model saved as 'best_cabin_model.pth'")
    
    # 7. Training tips
    print(f"\n6. Training Tips")
    print("-" * 15)
    print("✓ Use early stopping to prevent overfitting")
    print("✓ Apply gradient clipping for stable training")
    print("✓ Use learning rate scheduling")
    print("✓ Try different hidden sizes (32, 64, 128)")
    print("✓ Experiment with different ARM softmax combinations")
    print("✓ Consider data augmentation for small datasets")


if __name__ == "__main__":
    main()
