#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Basic usage example for Cabin wind power forecasting model.

This script demonstrates how to:
1. Load sample data
2. Initialize the Cabin model
3. Make predictions with different configurations
"""

import torch
import numpy as np
from cabin import Cabin
from cabin.utils import load_sample_data, normalize_data, create_windows

def main():
    print("Cabin Wind Power Forecasting - Basic Usage Example")
    print("=" * 55)
    
    # Set random seed for reproducibility
    torch.manual_seed(42)
    np.random.seed(42)
    
    # 1. Load sample data
    print("\n1. Loading sample data...")
    power_data, ambient_data = load_sample_data("synthetic")
    print(f"Power data shape: {power_data.shape}")
    print(f"Ambient data shape: {ambient_data.shape}")
    
    # 2. Prepare data
    print("\n2. Preparing data...")
    window_size = 10
    horizon = 1
    
    # Create windows
    power_windows, ambient_windows, targets = create_windows(
        power_data, ambient_data, window_size, horizon
    )
    
    print(f"Power windows shape: {power_windows.shape}")
    print(f"Ambient windows shape: {ambient_windows.shape}")
    print(f"Targets shape: {targets.shape}")
    
    # Convert to tensors
    power_tensor = torch.FloatTensor(power_windows[:32])  # Use first 32 samples
    ambient_tensor = torch.FloatTensor(ambient_windows[:32])
    targets_tensor = torch.FloatTensor(targets[:32])
    
    # 3. Test different Cabin configurations
    configurations = [
        {
            'name': 'Only-Target',
            'mode': 'only_target',
            'use_ambient': False,
            'description': 'Uses only historical power data'
        },
        {
            'name': 'Data-First',
            'mode': 'data_first', 
            'use_ambient': True,
            'description': 'Early fusion of power and ambient data'
        },
        {
            'name': 'Learning-First',
            'mode': 'learning_first',
            'use_ambient': True,
            'description': 'Separate learning then fusion'
        }
    ]
    
    for config in configurations:
        print(f"\n3. Testing {config['name']} Configuration")
        print("-" * 40)
        print(f"Description: {config['description']}")
        
        # Initialize model
        if config['use_ambient']:
            model = Cabin(
                input_window_size=window_size,
                input_vars=power_data.shape[1],  # Number of turbines
                output_window_size=horizon,
                output_vars=power_data.shape[1],
                ex_vars=ambient_data.shape[1],   # Number of ambient variables
                hidden_size=64,
                mode=config['mode']
            )
        else:
            model = Cabin(
                input_window_size=window_size,
                input_vars=power_data.shape[1],
                output_window_size=horizon,
                output_vars=power_data.shape[1],
                hidden_size=64,
                mode=config['mode']
            )
        
        # Print model info
        model_info = model.get_model_info()
        print(f"Model parameters: {model_info['total_parameters']:,}")
        
        # Make predictions
        model.eval()
        with torch.no_grad():
            if config['use_ambient']:
                predictions = model(power_tensor, ambient_tensor)
            else:
                predictions = model(power_tensor)
        
        print(f"Input shape: {power_tensor.shape}")
        if config['use_ambient']:
            print(f"Ambient shape: {ambient_tensor.shape}")
        print(f"Predictions shape: {predictions.shape}")
        print(f"Targets shape: {targets_tensor.shape}")
        
        # Calculate simple metrics
        mse = torch.mean((predictions - targets_tensor) ** 2).item()
        mae = torch.mean(torch.abs(predictions - targets_tensor)).item()
        
        print(f"MSE (untrained): {mse:.6f}")
        print(f"MAE (untrained): {mae:.6f}")
    
    # 4. Demonstrate model components
    print(f"\n4. Model Architecture Details")
    print("-" * 30)
    
    # Use data-first model for demonstration
    model = Cabin(
        input_window_size=window_size,
        input_vars=power_data.shape[1],
        output_window_size=horizon,
        output_vars=power_data.shape[1],
        ex_vars=ambient_data.shape[1],
        hidden_size=64,
        mode='data_first'
    )
    
    print("Model components:")
    for name, module in model.named_children():
        print(f"  - {name}: {type(module).__name__}")
        if hasattr(module, 'get_model_info'):
            info = module.get_model_info()
            print(f"    Parameters: {info.get('total_parameters', 'N/A')}")
    
    # 5. Show different softmax configurations
    print(f"\n5. ARM Softmax Configurations")
    print("-" * 32)
    
    softmax_configs = [
        {'sample': True, 'temporal': True, 'feature': True, 'name': 'All enabled'},
        {'sample': True, 'temporal': False, 'feature': False, 'name': 'Sample only'},
        {'sample': False, 'temporal': True, 'feature': False, 'name': 'Temporal only'},
        {'sample': False, 'temporal': False, 'feature': True, 'name': 'Feature only'},
    ]
    
    for config in softmax_configs:
        model = Cabin(
            input_window_size=window_size,
            input_vars=power_data.shape[1],
            output_window_size=horizon,
            output_vars=power_data.shape[1],
            ex_vars=ambient_data.shape[1],
            hidden_size=32,  # Smaller for demo
            mode='data_first',
            use_sample_softmax=config['sample'],
            use_temporal_softmax=config['temporal'],
            use_feature_wise_softmax=config['feature']
        )
        
        info = model.get_model_info()
        print(f"{config['name']}: {info['total_parameters']:,} parameters")
    
    print(f"\n6. Summary")
    print("-" * 10)
    print("✓ Successfully demonstrated all three Cabin configurations")
    print("✓ Showed model flexibility with different softmax settings")
    print("✓ All models can make predictions (though untrained)")
    print("\nNext steps:")
    print("- See training_example.py for full training pipeline")
    print("- See evaluation_example.py for comprehensive evaluation")


if __name__ == "__main__":
    main()
