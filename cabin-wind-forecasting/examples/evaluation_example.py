#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Model evaluation and analysis example for Cabin.

This script demonstrates:
1. Loading a trained model
2. Comprehensive evaluation on test data
3. Visualization of predictions
4. Error analysis and model interpretation
"""

import torch
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path

from cabin import Cabin
from cabin.utils import (
    load_sample_data, prepare_data, evaluate_model,
    print_metrics, calculate_metrics
)


def plot_predictions(y_true, y_pred, title="Predictions vs Actual", n_samples=100):
    """Plot predictions against actual values."""
    plt.figure(figsize=(12, 8))
    
    # Flatten arrays for plotting
    y_true_flat = y_true.flatten()[:n_samples]
    y_pred_flat = y_pred.flatten()[:n_samples]
    
    # Time series plot
    plt.subplot(2, 2, 1)
    plt.plot(y_true_flat, label='Actual', alpha=0.7)
    plt.plot(y_pred_flat, label='Predicted', alpha=0.7)
    plt.title(f'{title} - Time Series')
    plt.xlabel('Time Steps')
    plt.ylabel('Power Output')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # Scatter plot
    plt.subplot(2, 2, 2)
    plt.scatter(y_true_flat, y_pred_flat, alpha=0.6)
    min_val = min(y_true_flat.min(), y_pred_flat.min())
    max_val = max(y_true_flat.max(), y_pred_flat.max())
    plt.plot([min_val, max_val], [min_val, max_val], 'r--', alpha=0.8)
    plt.xlabel('Actual')
    plt.ylabel('Predicted')
    plt.title(f'{title} - Scatter Plot')
    plt.grid(True, alpha=0.3)
    
    # Residuals plot
    plt.subplot(2, 2, 3)
    residuals = y_pred_flat - y_true_flat
    plt.scatter(y_true_flat, residuals, alpha=0.6)
    plt.axhline(y=0, color='r', linestyle='--', alpha=0.8)
    plt.xlabel('Actual')
    plt.ylabel('Residuals')
    plt.title(f'{title} - Residuals')
    plt.grid(True, alpha=0.3)
    
    # Residuals histogram
    plt.subplot(2, 2, 4)
    plt.hist(residuals, bins=20, alpha=0.7, edgecolor='black')
    plt.xlabel('Residuals')
    plt.ylabel('Frequency')
    plt.title(f'{title} - Residual Distribution')
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()


def analyze_model_components(model, sample_input, sample_ambient=None):
    """Analyze the behavior of different model components."""
    print("\nModel Component Analysis")
    print("-" * 25)
    
    model.eval()
    with torch.no_grad():
        # Get model info
        info = model.get_model_info()
        print(f"Model mode: {info['mode']}")
        print(f"Total parameters: {info['total_parameters']:,}")
        
        # Analyze ARM outputs if possible
        if hasattr(model, 'arm0') and model.use_arm0:
            arm0_output = model.arm0(sample_input)
            print(f"ARM0 (sample softmax) output range: [{arm0_output.min():.4f}, {arm0_output.max():.4f}]")
            print(f"ARM0 output mean: {arm0_output.mean():.4f}")
        
        if hasattr(model, 'arm1') and model.use_arm1:
            arm1_output = model.arm1(sample_input)
            print(f"ARM1 (temporal softmax) output range: [{arm1_output.min():.4f}, {arm1_output.max():.4f}]")
            print(f"ARM1 output mean: {arm1_output.mean():.4f}")
        
        if hasattr(model, 'arm2') and model.use_arm2:
            arm2_output = model.arm2(sample_input)
            print(f"ARM2 (feature softmax) output range: [{arm2_output.min():.4f}, {arm2_output.max():.4f}]")
            print(f"ARM2 output mean: {arm2_output.mean():.4f}")


def compare_configurations():
    """Compare different Cabin configurations on the same data."""
    print("\nConfiguration Comparison")
    print("-" * 25)
    
    # Load data
    power_data, ambient_data = load_sample_data("synthetic")
    train_loader, val_loader, test_loader = prepare_data(
        power_data=power_data,
        ambient_data=ambient_data,
        window_size=10,
        horizon=1,
        batch_size=32
    )
    
    configurations = [
        ('only_target', False),
        ('data_first', True),
        ('learning_first', True)
    ]
    
    results = {}
    
    for mode, use_ambient in configurations:
        print(f"\nTesting {mode} configuration...")
        
        # Initialize model
        if use_ambient:
            model = Cabin(
                input_window_size=10,
                input_vars=1,
                output_window_size=1,
                output_vars=1,
                ex_vars=4,
                hidden_size=32,  # Smaller for quick demo
                mode=mode
            )
        else:
            model = Cabin(
                input_window_size=10,
                input_vars=1,
                output_window_size=1,
                output_vars=1,
                hidden_size=32,
                mode=mode
            )
        
        # Evaluate (untrained model for demo)
        metrics, loss = evaluate_model(model, test_loader)
        results[mode] = metrics
        
        print(f"MSE: {metrics['MSE']:.6f}")
        print(f"MAE: {metrics['MAE']:.6f}")
        print(f"R²: {metrics['R2']:.6f}")
    
    return results


def sensitivity_analysis():
    """Analyze model sensitivity to hyperparameters."""
    print("\nSensitivity Analysis")
    print("-" * 20)
    
    power_data, ambient_data = load_sample_data("synthetic")
    train_loader, val_loader, test_loader = prepare_data(
        power_data=power_data,
        ambient_data=ambient_data,
        window_size=10,
        horizon=1,
        batch_size=32
    )
    
    # Test different hidden sizes
    hidden_sizes = [16, 32, 64, 128]
    results = {}
    
    for hidden_size in hidden_sizes:
        model = Cabin(
            input_window_size=10,
            input_vars=1,
            output_window_size=1,
            output_vars=1,
            ex_vars=4,
            hidden_size=hidden_size,
            mode='data_first'
        )
        
        metrics, _ = evaluate_model(model, test_loader)
        results[hidden_size] = metrics
        
        print(f"Hidden size {hidden_size}: MSE = {metrics['MSE']:.6f}, "
              f"Parameters = {sum(p.numel() for p in model.parameters()):,}")
    
    return results


def main():
    print("Cabin Wind Power Forecasting - Evaluation Example")
    print("=" * 55)
    
    # Set random seed
    torch.manual_seed(42)
    np.random.seed(42)
    
    # 1. Load sample data and prepare
    print("\n1. Loading data...")
    power_data, ambient_data = load_sample_data("synthetic")
    train_loader, val_loader, test_loader = prepare_data(
        power_data=power_data,
        ambient_data=ambient_data,
        window_size=10,
        horizon=1,
        batch_size=32
    )
    
    # 2. Initialize and evaluate a model
    print("\n2. Model Evaluation...")
    model = Cabin(
        input_window_size=10,
        input_vars=1,
        output_window_size=1,
        output_vars=1,
        ex_vars=4,
        hidden_size=64,
        mode='data_first'
    )
    
    # Evaluate model
    metrics, loss = evaluate_model(model, test_loader)
    print_metrics(metrics, "Cabin Model Evaluation")
    
    # 3. Get predictions for visualization
    print("\n3. Generating predictions for visualization...")
    model.eval()
    all_predictions = []
    all_targets = []
    
    with torch.no_grad():
        for batch in test_loader:
            power_data, ambient_data, targets = batch
            predictions = model(power_data, ambient_data)
            all_predictions.append(predictions.numpy())
            all_targets.append(targets.numpy())
    
    all_predictions = np.concatenate(all_predictions, axis=0)
    all_targets = np.concatenate(all_targets, axis=0)
    
    # 4. Visualize results
    print("\n4. Creating visualizations...")
    try:
        plot_predictions(all_targets, all_predictions, "Cabin Model")
    except Exception as e:
        print(f"Visualization skipped (matplotlib not available): {e}")
    
    # 5. Analyze model components
    print("\n5. Analyzing model components...")
    sample_batch = next(iter(test_loader))
    sample_power = sample_batch[0][:1]  # First sample
    sample_ambient = sample_batch[1][:1] if len(sample_batch) > 2 else None
    
    analyze_model_components(model, sample_power, sample_ambient)
    
    # 6. Compare configurations
    print("\n6. Comparing configurations...")
    config_results = compare_configurations()
    
    print("\nConfiguration Comparison Summary:")
    print(f"{'Configuration':<15} {'MSE':<12} {'MAE':<12} {'R²':<8}")
    print("-" * 50)
    for config, metrics in config_results.items():
        print(f"{config:<15} {metrics['MSE']:<12.6f} {metrics['MAE']:<12.6f} {metrics['R2']:<8.4f}")
    
    # 7. Sensitivity analysis
    print("\n7. Sensitivity analysis...")
    sensitivity_results = sensitivity_analysis()
    
    # 8. Summary and recommendations
    print("\n8. Summary and Recommendations")
    print("-" * 32)
    print("✓ Model evaluation completed successfully")
    print("✓ All three configurations work as expected")
    print("✓ Model components are functioning properly")
    print("\nRecommendations:")
    print("- Train the model on real data for meaningful results")
    print("- Use cross-validation for robust performance estimation")
    print("- Consider ensemble methods for improved accuracy")
    print("- Monitor overfitting with validation curves")
    print("- Experiment with different ARM softmax combinations")


if __name__ == "__main__":
    main()
