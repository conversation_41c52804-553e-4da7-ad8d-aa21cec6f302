#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Data preparation utilities and examples for Cabin model.

This script demonstrates:
1. Loading different data formats
2. Data preprocessing and normalization
3. Creating appropriate data splits
4. Handling missing data
5. Data quality checks
"""

import numpy as np
import pandas as pd
import torch
from pathlib import Path
import matplotlib.pyplot as plt

from cabin.utils import (
    normalize_data, denormalize_data, create_windows, 
    prepare_data, load_sample_data
)


def create_sample_csv_data():
    """Create sample CSV files for demonstration."""
    print("Creating sample CSV data files...")
    
    # Generate synthetic data
    power_data, ambient_data = load_sample_data("synthetic")
    
    # Create date index
    dates = pd.date_range('2023-01-01', periods=len(power_data), freq='D')
    
    # Create power DataFrame
    power_df = pd.DataFrame({
        'date': dates,
        'turbine_1_power': power_data.flatten()
    })
    
    # Create ambient DataFrame
    ambient_df = pd.DataFrame({
        'date': dates,
        'wind_speed': ambient_data[:, 0],
        'temperature': ambient_data[:, 1],
        'pressure': ambient_data[:, 2],
        'humidity': ambient_data[:, 3]
    })
    
    # Save to CSV
    power_df.to_csv('sample_power_data.csv', index=False)
    ambient_df.to_csv('sample_ambient_data.csv', index=False)
    
    print("Created sample_power_data.csv and sample_ambient_data.csv")
    return power_df, ambient_df


def load_csv_data(power_file, ambient_file=None):
    """Load data from CSV files."""
    print(f"Loading data from {power_file}...")
    
    # Load power data
    power_df = pd.read_csv(power_file)
    print(f"Power data shape: {power_df.shape}")
    print(f"Power data columns: {list(power_df.columns)}")
    
    # Extract power values (assuming first column is date/time)
    power_columns = [col for col in power_df.columns if col.lower() not in ['date', 'time', 'datetime']]
    power_data = power_df[power_columns].values
    
    ambient_data = None
    if ambient_file:
        print(f"Loading ambient data from {ambient_file}...")
        ambient_df = pd.read_csv(ambient_file)
        print(f"Ambient data shape: {ambient_df.shape}")
        print(f"Ambient data columns: {list(ambient_df.columns)}")
        
        # Extract ambient values
        ambient_columns = [col for col in ambient_df.columns if col.lower() not in ['date', 'time', 'datetime']]
        ambient_data = ambient_df[ambient_columns].values
    
    return power_data, ambient_data


def check_data_quality(power_data, ambient_data=None):
    """Perform data quality checks."""
    print("\nData Quality Checks")
    print("-" * 20)
    
    # Check power data
    print("Power Data:")
    print(f"  Shape: {power_data.shape}")
    print(f"  Missing values: {np.isnan(power_data).sum()}")
    print(f"  Min value: {power_data.min():.4f}")
    print(f"  Max value: {power_data.max():.4f}")
    print(f"  Mean: {power_data.mean():.4f}")
    print(f"  Std: {power_data.std():.4f}")
    
    # Check for negative values (unusual for power data)
    negative_count = (power_data < 0).sum()
    if negative_count > 0:
        print(f"  WARNING: {negative_count} negative values found!")
    
    # Check for outliers (values > 3 std from mean)
    z_scores = np.abs((power_data - power_data.mean()) / power_data.std())
    outliers = (z_scores > 3).sum()
    print(f"  Outliers (>3σ): {outliers}")
    
    if ambient_data is not None:
        print("\nAmbient Data:")
        print(f"  Shape: {ambient_data.shape}")
        print(f"  Missing values: {np.isnan(ambient_data).sum()}")
        print(f"  Min values: {ambient_data.min(axis=0)}")
        print(f"  Max values: {ambient_data.max(axis=0)}")
        print(f"  Mean values: {ambient_data.mean(axis=0)}")


def handle_missing_data(data, method='interpolate'):
    """Handle missing data in the dataset."""
    print(f"\nHandling missing data using method: {method}")
    
    missing_count = np.isnan(data).sum()
    if missing_count == 0:
        print("No missing data found.")
        return data
    
    print(f"Found {missing_count} missing values")
    
    if method == 'interpolate':
        # Linear interpolation
        df = pd.DataFrame(data)
        df_filled = df.interpolate(method='linear', axis=0)
        return df_filled.values
    
    elif method == 'forward_fill':
        # Forward fill
        df = pd.DataFrame(data)
        df_filled = df.fillna(method='ffill')
        return df_filled.values
    
    elif method == 'mean':
        # Fill with column means
        col_means = np.nanmean(data, axis=0)
        data_filled = data.copy()
        for i in range(data.shape[1]):
            mask = np.isnan(data[:, i])
            data_filled[mask, i] = col_means[i]
        return data_filled
    
    else:
        raise ValueError(f"Unknown method: {method}")


def create_train_val_test_splits(data, train_ratio=0.7, val_ratio=0.1):
    """Create chronological train/validation/test splits."""
    print(f"\nCreating data splits (train: {train_ratio}, val: {val_ratio}, test: {1-train_ratio-val_ratio})")
    
    n_samples = len(data)
    train_end = int(n_samples * train_ratio)
    val_end = int(n_samples * (train_ratio + val_ratio))
    
    train_data = data[:train_end]
    val_data = data[train_end:val_end]
    test_data = data[val_end:]
    
    print(f"Train samples: {len(train_data)}")
    print(f"Validation samples: {len(val_data)}")
    print(f"Test samples: {len(test_data)}")
    
    return train_data, val_data, test_data


def visualize_data(power_data, ambient_data=None, n_samples=100):
    """Visualize the data."""
    print(f"\nVisualizing data (first {n_samples} samples)...")
    
    try:
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        # Power data time series
        axes[0, 0].plot(power_data[:n_samples, 0])
        axes[0, 0].set_title('Power Data Time Series')
        axes[0, 0].set_xlabel('Time Steps')
        axes[0, 0].set_ylabel('Power Output')
        axes[0, 0].grid(True, alpha=0.3)
        
        # Power data histogram
        axes[0, 1].hist(power_data.flatten(), bins=30, alpha=0.7, edgecolor='black')
        axes[0, 1].set_title('Power Data Distribution')
        axes[0, 1].set_xlabel('Power Output')
        axes[0, 1].set_ylabel('Frequency')
        axes[0, 1].grid(True, alpha=0.3)
        
        if ambient_data is not None:
            # Ambient data time series
            for i in range(min(4, ambient_data.shape[1])):
                axes[1, 0].plot(ambient_data[:n_samples, i], label=f'Var {i+1}', alpha=0.7)
            axes[1, 0].set_title('Ambient Variables Time Series')
            axes[1, 0].set_xlabel('Time Steps')
            axes[1, 0].set_ylabel('Value')
            axes[1, 0].legend()
            axes[1, 0].grid(True, alpha=0.3)
            
            # Correlation heatmap
            if ambient_data.shape[1] > 1:
                combined_data = np.column_stack([power_data[:, 0], ambient_data])
                corr_matrix = np.corrcoef(combined_data.T)
                im = axes[1, 1].imshow(corr_matrix, cmap='coolwarm', vmin=-1, vmax=1)
                axes[1, 1].set_title('Correlation Matrix')
                plt.colorbar(im, ax=axes[1, 1])
        else:
            axes[1, 0].text(0.5, 0.5, 'No ambient data', ha='center', va='center', transform=axes[1, 0].transAxes)
            axes[1, 1].text(0.5, 0.5, 'No ambient data', ha='center', va='center', transform=axes[1, 1].transAxes)
        
        plt.tight_layout()
        plt.show()
        
    except Exception as e:
        print(f"Visualization skipped: {e}")


def main():
    print("Cabin Data Preparation Example")
    print("=" * 35)
    
    # 1. Create sample data files
    print("\n1. Creating sample data files...")
    power_df, ambient_df = create_sample_csv_data()
    
    # 2. Load data from CSV
    print("\n2. Loading data from CSV files...")
    power_data, ambient_data = load_csv_data('sample_power_data.csv', 'sample_ambient_data.csv')
    
    # 3. Data quality checks
    check_data_quality(power_data, ambient_data)
    
    # 4. Handle missing data (simulate some missing values)
    print("\n4. Simulating and handling missing data...")
    power_with_missing = power_data.copy()
    # Randomly set 5% of values to NaN
    missing_indices = np.random.choice(power_with_missing.size, 
                                     size=int(0.05 * power_with_missing.size), 
                                     replace=False)
    power_with_missing.flat[missing_indices] = np.nan
    
    power_cleaned = handle_missing_data(power_with_missing, method='interpolate')
    print(f"Missing values before: {np.isnan(power_with_missing).sum()}")
    print(f"Missing values after: {np.isnan(power_cleaned).sum()}")
    
    # 5. Normalization
    print("\n5. Data normalization...")
    power_normalized, power_scaler = normalize_data(power_data)
    ambient_normalized, ambient_scaler = normalize_data(ambient_data)
    
    print(f"Power data range before normalization: [{power_data.min():.4f}, {power_data.max():.4f}]")
    print(f"Power data range after normalization: [{power_normalized.min():.4f}, {power_normalized.max():.4f}]")
    
    # Test denormalization
    power_denormalized = denormalize_data(power_normalized, power_scaler)
    print(f"Denormalization error: {np.mean(np.abs(power_data - power_denormalized)):.8f}")
    
    # 6. Create windows
    print("\n6. Creating sliding windows...")
    power_windows, ambient_windows, targets = create_windows(
        power_normalized, ambient_normalized, window_size=10, horizon=1
    )
    
    print(f"Created {len(power_windows)} windows")
    print(f"Power windows shape: {power_windows.shape}")
    print(f"Ambient windows shape: {ambient_windows.shape}")
    print(f"Targets shape: {targets.shape}")
    
    # 7. Create data splits
    print("\n7. Creating train/validation/test splits...")
    train_power, val_power, test_power = create_train_val_test_splits(power_normalized)
    train_ambient, val_ambient, test_ambient = create_train_val_test_splits(ambient_normalized)
    
    # 8. Prepare data loaders
    print("\n8. Preparing data loaders...")
    train_loader, val_loader, test_loader = prepare_data(
        power_data=power_data,
        ambient_data=ambient_data,
        window_size=10,
        horizon=1,
        batch_size=16
    )
    
    print(f"Train loader: {len(train_loader)} batches")
    print(f"Validation loader: {len(val_loader)} batches")
    print(f"Test loader: {len(test_loader)} batches")
    
    # 9. Visualize data
    print("\n9. Visualizing data...")
    visualize_data(power_data, ambient_data)
    
    # 10. Data preparation tips
    print("\n10. Data Preparation Best Practices")
    print("-" * 35)
    print("✓ Always check for missing values and outliers")
    print("✓ Use chronological splits for time series data")
    print("✓ Normalize data to [0,1] or standardize to mean=0, std=1")
    print("✓ Keep normalization parameters for denormalization")
    print("✓ Consider seasonal patterns in your data")
    print("✓ Validate data quality before training")
    print("✓ Use appropriate window sizes based on your forecasting horizon")
    
    # Clean up sample files
    try:
        Path('sample_power_data.csv').unlink()
        Path('sample_ambient_data.csv').unlink()
        print("\nCleaned up sample CSV files")
    except:
        pass


if __name__ == "__main__":
    main()
