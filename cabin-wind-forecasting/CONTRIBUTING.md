# Contributing to Cabin

We welcome contributions to the Cabin wind power forecasting framework! This document provides guidelines for contributing to the project.

## Getting Started

1. Fork the repository on GitHub
2. Clone your fork locally
3. Create a new branch for your feature or bug fix
4. Make your changes
5. Test your changes
6. Submit a pull request

## Development Setup

### Prerequisites

- Python 3.8+
- PyTorch 1.9+
- Git

### Installation for Development

```bash
git clone https://github.com/xiufengliu/cabin-wind-forecasting.git
cd cabin-wind-forecasting

# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install in development mode
pip install -e ".[dev]"
```

## Code Style

We follow PEP 8 style guidelines with some modifications:

- Line length: 100 characters
- Use type hints where possible
- Document all public functions and classes
- Use meaningful variable names

### Code Formatting

We use `black` for code formatting:

```bash
black cabin/ examples/ tests/
```

### Linting

We use `flake8` for linting:

```bash
flake8 cabin/ examples/ tests/
```

### Type Checking

We use `mypy` for type checking:

```bash
mypy cabin/
```

## Testing

### Running Tests

```bash
pytest tests/ -v
```

### Writing Tests

- Write tests for all new functionality
- Use descriptive test names
- Include both positive and negative test cases
- Test edge cases and error conditions

Example test structure:

```python
def test_cabin_model_initialization():
    """Test that Cabin model initializes correctly."""
    model = Cabin(
        input_window_size=10,
        input_vars=1,
        output_window_size=1,
        output_vars=1,
        mode='only_target'
    )
    assert model.mode == 'only_target'
    assert model.input_window_size == 10
```

## Documentation

### Docstring Format

Use Google-style docstrings:

```python
def example_function(param1: int, param2: str) -> bool:
    """
    Brief description of the function.
    
    Args:
        param1: Description of param1.
        param2: Description of param2.
        
    Returns:
        Description of return value.
        
    Raises:
        ValueError: Description of when this error is raised.
    """
    pass
```

### Adding Examples

When adding new features, include:

1. Code examples in docstrings
2. Example scripts in the `examples/` directory
3. Updates to the README if necessary

## Pull Request Process

1. **Create a descriptive branch name**: `feature/new-activation-function` or `fix/memory-leak`

2. **Write clear commit messages**:
   ```
   Add support for custom activation functions
   
   - Implement custom activation function interface
   - Add tests for new activation functions
   - Update documentation with examples
   ```

3. **Update documentation** if your changes affect the public API

4. **Add tests** for new functionality

5. **Ensure all tests pass**:
   ```bash
   pytest tests/
   black --check cabin/ examples/ tests/
   flake8 cabin/ examples/ tests/
   mypy cabin/
   ```

6. **Update the changelog** if your changes are user-facing

7. **Submit the pull request** with:
   - Clear title and description
   - Reference to any related issues
   - Screenshots if applicable (for UI changes)

## Types of Contributions

### Bug Reports

When reporting bugs, please include:

- Python version
- PyTorch version
- Operating system
- Minimal code example that reproduces the bug
- Expected vs. actual behavior
- Full error traceback

### Feature Requests

When requesting features, please include:

- Clear description of the feature
- Use case and motivation
- Proposed API (if applicable)
- Willingness to implement the feature

### Code Contributions

We welcome contributions in these areas:

- **New model components**: Additional ARM variants, new CAV modules
- **Optimization improvements**: Better training algorithms, memory optimizations
- **Data utilities**: New preprocessing functions, data loaders
- **Evaluation metrics**: Additional forecasting metrics
- **Documentation**: Tutorials, examples, API documentation
- **Testing**: Unit tests, integration tests, benchmarks

## Code Review Process

1. All submissions require review from at least one maintainer
2. Reviews focus on:
   - Code correctness and efficiency
   - Test coverage
   - Documentation quality
   - Adherence to project standards
3. Address review feedback promptly
4. Maintainers may request changes before merging

## Release Process

1. Version numbers follow semantic versioning (MAJOR.MINOR.PATCH)
2. Releases are tagged and published to PyPI
3. Release notes summarize changes and breaking changes
4. Documentation is updated for each release

## Community Guidelines

- Be respectful and inclusive
- Help others learn and contribute
- Focus on constructive feedback
- Follow the project's code of conduct

## Getting Help

- **Documentation**: Check the README and examples first
- **Issues**: Search existing issues before creating new ones
- **Discussions**: Use GitHub Discussions for questions and ideas
- **Email**: Contact maintainers for sensitive issues

## Recognition

Contributors are recognized in:

- CONTRIBUTORS.md file
- Release notes for significant contributions
- GitHub contributor statistics

Thank you for contributing to Cabin!
