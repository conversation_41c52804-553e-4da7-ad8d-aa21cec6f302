#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""Setup script for Cabin wind power forecasting framework."""

from setuptools import setup, find_packages
import os

# Read the README file
def read_readme():
    with open("README.md", "r", encoding="utf-8") as fh:
        return fh.read()

# Read requirements
def read_requirements():
    with open("requirements.txt", "r", encoding="utf-8") as fh:
        return [line.strip() for line in fh if line.strip() and not line.startswith("#")]

setup(
    name="cabin-wind-forecasting",
    version="1.0.0",
    author="<PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>",
    author_email="<EMAIL>",
    description="A Collaborative and Adaptive Framework for Wind Power Forecasting Integrating Ambient Variables",
    long_description=read_readme(),
    long_description_content_type="text/markdown",
    url="https://github.com/yourusername/cabin-wind-forecasting",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Science/Research",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
        "Topic :: Scientific/Engineering :: Information Analysis",
        "Topic :: Software Development :: Libraries :: Python Modules",
    ],
    python_requires=">=3.8",
    install_requires=read_requirements(),
    extras_require={
        "dev": [
            "pytest>=6.0",
            "pytest-cov>=2.0",
            "black>=21.0",
            "flake8>=3.8",
            "isort>=5.0",
            "mypy>=0.800",
        ],
        "examples": [
            "matplotlib>=3.3",
            "seaborn>=0.11",
            "jupyter>=1.0",
            "notebook>=6.0",
        ],
    },
    include_package_data=True,
    package_data={
        "cabin": ["*.txt", "*.md"],
    },
    entry_points={
        "console_scripts": [
            "cabin-train=cabin.cli:train",
            "cabin-predict=cabin.cli:predict",
        ],
    },
    keywords=[
        "wind power forecasting",
        "time series",
        "deep learning",
        "pytorch",
        "renewable energy",
        "ambient variables",
        "collaborative learning",
        "adaptive modeling",
        "kolmogorov-arnold networks",
    ],
    project_urls={
        "Bug Reports": "https://github.com/yourusername/cabin-wind-forecasting/issues",
        "Source": "https://github.com/yourusername/cabin-wind-forecasting",
        "Documentation": "https://cabin-wind-forecasting.readthedocs.io/",
    },
)
