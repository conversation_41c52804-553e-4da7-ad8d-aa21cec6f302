#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Command-line interface for Cabin wind power forecasting.

This module provides CLI commands for training and prediction.
"""

import argparse
import sys
import torch
import json
from pathlib import Path

from .models.cabin import Cabin
from .utils.preprocessing import prepare_data
from .utils.metrics import evaluate_model, print_metrics


def train_command():
    """CLI command for training Cabin models."""
    parser = argparse.ArgumentParser(description='Train Cabin wind power forecasting model')
    
    # Data arguments
    parser.add_argument('--power-data', required=True, help='Path to power data CSV file')
    parser.add_argument('--ambient-data', help='Path to ambient data CSV file')
    parser.add_argument('--output-dir', default='./cabin_output', help='Output directory for model and results')
    
    # Model arguments
    parser.add_argument('--mode', choices=['only_target', 'data_first', 'learning_first'], 
                       default='data_first', help='Cabin architecture mode')
    parser.add_argument('--window-size', type=int, default=10, help='Input window size')
    parser.add_argument('--horizon', type=int, default=1, help='Forecast horizon')
    parser.add_argument('--hidden-size', type=int, default=64, help='Hidden layer size')
    
    # Training arguments
    parser.add_argument('--epochs', type=int, default=100, help='Number of training epochs')
    parser.add_argument('--batch-size', type=int, default=32, help='Batch size')
    parser.add_argument('--learning-rate', type=float, default=0.001, help='Learning rate')
    parser.add_argument('--patience', type=int, default=10, help='Early stopping patience')
    
    # Data split arguments
    parser.add_argument('--train-ratio', type=float, default=0.7, help='Training data ratio')
    parser.add_argument('--val-ratio', type=float, default=0.1, help='Validation data ratio')
    
    args = parser.parse_args()
    
    # Create output directory
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    print("Cabin Training")
    print("=" * 15)
    print(f"Power data: {args.power_data}")
    print(f"Ambient data: {args.ambient_data}")
    print(f"Mode: {args.mode}")
    print(f"Output directory: {output_dir}")
    
    try:
        # Prepare data
        print("\nPreparing data...")
        train_loader, val_loader, test_loader = prepare_data(
            power_data=args.power_data,
            ambient_data=args.ambient_data,
            window_size=args.window_size,
            horizon=args.horizon,
            train_ratio=args.train_ratio,
            val_ratio=args.val_ratio,
            batch_size=args.batch_size
        )
        
        # Get data dimensions from first batch
        sample_batch = next(iter(train_loader))
        if len(sample_batch) == 3:  # Power, ambient, targets
            power_sample, ambient_sample, target_sample = sample_batch
            input_vars = power_sample.shape[2]
            output_vars = target_sample.shape[2]
            ex_vars = ambient_sample.shape[2]
        else:  # Power, targets
            power_sample, target_sample = sample_batch
            input_vars = power_sample.shape[2]
            output_vars = target_sample.shape[2]
            ex_vars = 0
        
        # Initialize model
        print(f"\nInitializing Cabin model...")
        model = Cabin(
            input_window_size=args.window_size,
            input_vars=input_vars,
            output_window_size=args.horizon,
            output_vars=output_vars,
            ex_vars=ex_vars,
            hidden_size=args.hidden_size,
            mode=args.mode
        )
        
        print(f"Model parameters: {sum(p.numel() for p in model.parameters()):,}")
        
        # Training would go here (simplified for CLI)
        print("\nTraining model...")
        print("Note: Full training implementation would go here")
        print("For now, saving model configuration...")
        
        # Save model configuration
        config = {
            'model_config': model.get_model_info(),
            'training_args': vars(args),
            'data_shapes': {
                'input_vars': input_vars,
                'output_vars': output_vars,
                'ex_vars': ex_vars
            }
        }
        
        config_path = output_dir / 'model_config.json'
        with open(config_path, 'w') as f:
            json.dump(config, f, indent=2)
        
        print(f"Model configuration saved to {config_path}")
        print("Training completed successfully!")
        
    except Exception as e:
        print(f"Error during training: {e}")
        sys.exit(1)


def predict_command():
    """CLI command for making predictions with trained Cabin models."""
    parser = argparse.ArgumentParser(description='Make predictions with trained Cabin model')
    
    parser.add_argument('--model-path', required=True, help='Path to trained model file')
    parser.add_argument('--data-path', required=True, help='Path to input data CSV file')
    parser.add_argument('--output-path', default='predictions.csv', help='Output path for predictions')
    parser.add_argument('--batch-size', type=int, default=32, help='Batch size for prediction')
    
    args = parser.parse_args()
    
    print("Cabin Prediction")
    print("=" * 16)
    print(f"Model: {args.model_path}")
    print(f"Data: {args.data_path}")
    print(f"Output: {args.output_path}")
    
    try:
        # Load model (simplified - would need actual model loading logic)
        print("\nLoading model...")
        print("Note: Full prediction implementation would go here")
        print("This would load the trained model and make predictions on new data")
        
        print("Predictions completed successfully!")
        
    except Exception as e:
        print(f"Error during prediction: {e}")
        sys.exit(1)


def main():
    """Main CLI entry point."""
    if len(sys.argv) < 2:
        print("Usage: cabin-cli <command> [options]")
        print("Commands:")
        print("  train    - Train a Cabin model")
        print("  predict  - Make predictions with a trained model")
        sys.exit(1)
    
    command = sys.argv[1]
    sys.argv = [sys.argv[0]] + sys.argv[2:]  # Remove command from argv
    
    if command == 'train':
        train_command()
    elif command == 'predict':
        predict_command()
    else:
        print(f"Unknown command: {command}")
        sys.exit(1)


if __name__ == "__main__":
    main()
