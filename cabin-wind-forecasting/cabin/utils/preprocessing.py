#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Data preprocessing utilities for Cabin model.

This module provides functions for data normalization, window creation,
and dataset preparation for wind power forecasting.
"""

import numpy as np
import pandas as pd
import torch
from torch.utils.data import Dataset, DataLoader
from typing import <PERSON><PERSON>, Optional, Union
from sklearn.preprocessing import MinMaxScaler


def normalize_data(data: Union[np.ndarray, pd.DataFrame], 
                  scaler: Optional[MinMaxScaler] = None,
                  fit_scaler: bool = True) -> Tuple[np.ndarray, MinMaxScaler]:
    """
    Normalize data using Min-Max scaling to [0, 1] range.
    
    Args:
        data: Input data to normalize.
        scaler: Pre-fitted scaler to use. If None, creates a new one.
        fit_scaler: Whether to fit the scaler on the data.
        
    Returns:
        Tuple of (normalized_data, scaler).
    """
    if isinstance(data, pd.DataFrame):
        data = data.values
    
    if scaler is None:
        scaler = MinMaxScaler(feature_range=(0, 1))
    
    if fit_scaler:
        normalized_data = scaler.fit_transform(data)
    else:
        normalized_data = scaler.transform(data)
    
    return normalized_data, scaler


def denormalize_data(normalized_data: np.ndarray, scaler: MinMaxScaler) -> np.ndarray:
    """
    Denormalize data using the provided scaler.
    
    Args:
        normalized_data: Normalized data to denormalize.
        scaler: Fitted scaler used for normalization.
        
    Returns:
        Denormalized data.
    """
    return scaler.inverse_transform(normalized_data)


def create_windows(power_data: np.ndarray, 
                  ambient_data: Optional[np.ndarray] = None,
                  window_size: int = 10, 
                  horizon: int = 1,
                  step: int = 1) -> Tuple[np.ndarray, Optional[np.ndarray], np.ndarray]:
    """
    Create sliding windows from time series data.
    
    Args:
        power_data: Historical power data of shape [time_steps, num_turbines].
        ambient_data: Ambient variable data of shape [time_steps, num_ambient_vars].
        window_size: Size of the input window (number of historical time steps).
        horizon: Forecast horizon (number of future time steps to predict).
        step: Step size for sliding window.
        
    Returns:
        Tuple of (power_windows, ambient_windows, targets).
        - power_windows: [num_windows, window_size, num_turbines]
        - ambient_windows: [num_windows, window_size, num_ambient_vars] or None
        - targets: [num_windows, horizon, num_turbines]
    """
    n_samples = len(power_data)
    n_windows = (n_samples - window_size - horizon + 1) // step
    
    if n_windows <= 0:
        raise ValueError(f"Not enough data to create windows. Need at least {window_size + horizon} samples, got {n_samples}")
    
    # Create power data windows
    power_windows = []
    targets = []
    
    for i in range(0, n_windows * step, step):
        # Input window
        power_window = power_data[i:i + window_size]
        power_windows.append(power_window)
        
        # Target window
        target = power_data[i + window_size:i + window_size + horizon]
        targets.append(target)
    
    power_windows = np.array(power_windows)
    targets = np.array(targets)
    
    # Create ambient data windows if provided
    ambient_windows = None
    if ambient_data is not None:
        ambient_windows = []
        for i in range(0, n_windows * step, step):
            ambient_window = ambient_data[i:i + window_size]
            ambient_windows.append(ambient_window)
        ambient_windows = np.array(ambient_windows)
    
    return power_windows, ambient_windows, targets


class WindPowerDataset(Dataset):
    """
    PyTorch Dataset for wind power forecasting.
    """
    
    def __init__(self, power_windows: np.ndarray, 
                 targets: np.ndarray,
                 ambient_windows: Optional[np.ndarray] = None):
        """
        Initialize the dataset.
        
        Args:
            power_windows: Power data windows of shape [num_windows, window_size, num_turbines].
            targets: Target values of shape [num_windows, horizon, num_turbines].
            ambient_windows: Ambient data windows of shape [num_windows, window_size, num_ambient_vars].
        """
        self.power_windows = torch.FloatTensor(power_windows)
        self.targets = torch.FloatTensor(targets)
        self.ambient_windows = torch.FloatTensor(ambient_windows) if ambient_windows is not None else None
        
    def __len__(self):
        return len(self.power_windows)
    
    def __getitem__(self, idx):
        if self.ambient_windows is not None:
            return self.power_windows[idx], self.ambient_windows[idx], self.targets[idx]
        else:
            return self.power_windows[idx], self.targets[idx]


def prepare_data(power_data: Union[str, np.ndarray, pd.DataFrame],
                ambient_data: Optional[Union[str, np.ndarray, pd.DataFrame]] = None,
                window_size: int = 10,
                horizon: int = 1,
                train_ratio: float = 0.7,
                val_ratio: float = 0.1,
                batch_size: int = 32,
                normalize: bool = True) -> Tuple[DataLoader, DataLoader, DataLoader]:
    """
    Prepare data loaders for training, validation, and testing.
    
    Args:
        power_data: Power data file path or array/DataFrame.
        ambient_data: Ambient data file path or array/DataFrame.
        window_size: Size of input windows.
        horizon: Forecast horizon.
        train_ratio: Ratio of data for training.
        val_ratio: Ratio of data for validation.
        batch_size: Batch size for data loaders.
        normalize: Whether to normalize the data.
        
    Returns:
        Tuple of (train_loader, val_loader, test_loader).
    """
    # Load data
    if isinstance(power_data, str):
        power_df = pd.read_csv(power_data)
        power_array = power_df.values
    elif isinstance(power_data, pd.DataFrame):
        power_array = power_data.values
    else:
        power_array = power_data
    
    ambient_array = None
    if ambient_data is not None:
        if isinstance(ambient_data, str):
            ambient_df = pd.read_csv(ambient_data)
            ambient_array = ambient_df.values
        elif isinstance(ambient_data, pd.DataFrame):
            ambient_array = ambient_data.values
        else:
            ambient_array = ambient_data
    
    # Normalize data
    power_scaler = None
    ambient_scaler = None
    
    if normalize:
        power_array, power_scaler = normalize_data(power_array)
        if ambient_array is not None:
            ambient_array, ambient_scaler = normalize_data(ambient_array)
    
    # Split data chronologically
    n_samples = len(power_array)
    train_end = int(n_samples * train_ratio)
    val_end = int(n_samples * (train_ratio + val_ratio))
    
    train_power = power_array[:train_end]
    val_power = power_array[train_end:val_end]
    test_power = power_array[val_end:]
    
    train_ambient = ambient_array[:train_end] if ambient_array is not None else None
    val_ambient = ambient_array[train_end:val_end] if ambient_array is not None else None
    test_ambient = ambient_array[val_end:] if ambient_array is not None else None
    
    # Create windows
    train_power_windows, train_ambient_windows, train_targets = create_windows(
        train_power, train_ambient, window_size, horizon)
    val_power_windows, val_ambient_windows, val_targets = create_windows(
        val_power, val_ambient, window_size, horizon)
    test_power_windows, test_ambient_windows, test_targets = create_windows(
        test_power, test_ambient, window_size, horizon)
    
    # Create datasets
    train_dataset = WindPowerDataset(train_power_windows, train_targets, train_ambient_windows)
    val_dataset = WindPowerDataset(val_power_windows, val_targets, val_ambient_windows)
    test_dataset = WindPowerDataset(test_power_windows, test_targets, test_ambient_windows)
    
    # Create data loaders
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False)
    test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)
    
    return train_loader, val_loader, test_loader


def load_sample_data(dataset_name: str = "synthetic") -> Tuple[np.ndarray, np.ndarray]:
    """
    Load sample data for testing and examples.
    
    Args:
        dataset_name: Name of the dataset to load ("synthetic" for generated data).
        
    Returns:
        Tuple of (power_data, ambient_data).
    """
    if dataset_name == "synthetic":
        # Generate synthetic wind power and ambient data
        np.random.seed(42)
        n_days = 365
        
        # Generate synthetic power data (1 turbine)
        time = np.arange(n_days)
        seasonal_pattern = 0.3 * np.sin(2 * np.pi * time / 365)  # Yearly seasonality
        daily_pattern = 0.2 * np.sin(2 * np.pi * time / 7)       # Weekly pattern
        noise = 0.1 * np.random.randn(n_days)
        power_data = 0.5 + seasonal_pattern + daily_pattern + noise
        power_data = np.clip(power_data, 0, 1).reshape(-1, 1)
        
        # Generate synthetic ambient data (4 variables: wind speed, temperature, pressure, humidity)
        wind_speed = 0.6 + 0.2 * np.sin(2 * np.pi * time / 365) + 0.1 * np.random.randn(n_days)
        temperature = 0.5 + 0.3 * np.sin(2 * np.pi * (time - 90) / 365) + 0.05 * np.random.randn(n_days)
        pressure = 0.5 + 0.1 * np.random.randn(n_days)
        humidity = 0.4 + 0.2 * np.sin(2 * np.pi * time / 30) + 0.1 * np.random.randn(n_days)
        
        ambient_data = np.column_stack([
            np.clip(wind_speed, 0, 1),
            np.clip(temperature, 0, 1),
            np.clip(pressure, 0, 1),
            np.clip(humidity, 0, 1)
        ])
        
        return power_data, ambient_data
    
    else:
        raise ValueError(f"Unknown dataset: {dataset_name}")
