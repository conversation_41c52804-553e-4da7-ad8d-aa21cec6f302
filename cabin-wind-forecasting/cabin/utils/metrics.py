#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Evaluation metrics for wind power forecasting.

This module provides functions to calculate various performance metrics
used in wind power forecasting evaluation.
"""

import numpy as np
import torch
import torch.nn as nn
from typing import Dict, Union, Tuple
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score


def calculate_metrics(y_true: Union[np.ndarray, torch.Tensor], 
                     y_pred: Union[np.ndarray, torch.Tensor],
                     denormalize: bool = False,
                     scaler = None) -> Dict[str, float]:
    """
    Calculate comprehensive evaluation metrics for wind power forecasting.
    
    Args:
        y_true: True values of shape [n_samples, horizon, n_vars] or [n_samples].
        y_pred: Predicted values of same shape as y_true.
        denormalize: Whether to denormalize the data before calculating metrics.
        scaler: Scaler used for normalization (required if denormalize=True).
        
    Returns:
        Dictionary containing calculated metrics.
    """
    # Convert to numpy arrays if needed
    if isinstance(y_true, torch.Tensor):
        y_true = y_true.detach().cpu().numpy()
    if isinstance(y_pred, torch.Tensor):
        y_pred = y_pred.detach().cpu().numpy()
    
    # Denormalize if requested
    if denormalize and scaler is not None:
        original_shape_true = y_true.shape
        original_shape_pred = y_pred.shape
        
        # Reshape for denormalization
        y_true_flat = y_true.reshape(-1, y_true.shape[-1])
        y_pred_flat = y_pred.reshape(-1, y_pred.shape[-1])
        
        y_true = scaler.inverse_transform(y_true_flat).reshape(original_shape_true)
        y_pred = scaler.inverse_transform(y_pred_flat).reshape(original_shape_pred)
    
    # Flatten arrays for metric calculation
    y_true_flat = y_true.flatten()
    y_pred_flat = y_pred.flatten()
    
    # Calculate metrics
    mse = mean_squared_error(y_true_flat, y_pred_flat)
    mae = mean_absolute_error(y_true_flat, y_pred_flat)
    rmse = np.sqrt(mse)
    
    # Coefficient of Variation of RMSE
    mean_true = np.mean(y_true_flat)
    cv_rmse = rmse / mean_true if mean_true != 0 else float('inf')
    
    # R-squared score
    r2 = r2_score(y_true_flat, y_pred_flat)
    
    # Mean Absolute Percentage Error (MAPE)
    mape = np.mean(np.abs((y_true_flat - y_pred_flat) / (y_true_flat + 1e-8))) * 100
    
    # Normalized RMSE (NRMSE) - normalized by the range
    data_range = np.max(y_true_flat) - np.min(y_true_flat)
    nrmse = rmse / data_range if data_range != 0 else float('inf')
    
    return {
        'MSE': mse,
        'MAE': mae,
        'RMSE': rmse,
        'CV_RMSE': cv_rmse,
        'R2': r2,
        'MAPE': mape,
        'NRMSE': nrmse,
        'Mean_True': mean_true,
        'Std_True': np.std(y_true_flat),
        'Mean_Pred': np.mean(y_pred_flat),
        'Std_Pred': np.std(y_pred_flat)
    }


def evaluate_model(model: nn.Module, 
                  data_loader,
                  device: str = 'cpu',
                  criterion = None,
                  denormalize: bool = False,
                  scaler = None) -> Tuple[Dict[str, float], float]:
    """
    Evaluate a model on a dataset.
    
    Args:
        model: The model to evaluate.
        data_loader: DataLoader containing the evaluation data.
        device: Device to run evaluation on.
        criterion: Loss function to use. If None, uses MSE.
        denormalize: Whether to denormalize predictions for metric calculation.
        scaler: Scaler for denormalization.
        
    Returns:
        Tuple of (metrics_dict, average_loss).
    """
    if criterion is None:
        criterion = nn.MSELoss()
    
    model.eval()
    model.to(device)
    
    all_predictions = []
    all_targets = []
    total_loss = 0.0
    num_batches = 0
    
    with torch.no_grad():
        for batch in data_loader:
            if len(batch) == 3:  # Power, ambient, targets
                power_data, ambient_data, targets = batch
                power_data = power_data.to(device)
                ambient_data = ambient_data.to(device)
                targets = targets.to(device)
                
                predictions = model(power_data, ambient_data)
            else:  # Power, targets (only-target mode)
                power_data, targets = batch
                power_data = power_data.to(device)
                targets = targets.to(device)
                
                predictions = model(power_data)
            
            # Calculate loss
            loss = criterion(predictions, targets)
            total_loss += loss.item()
            num_batches += 1
            
            # Store predictions and targets
            all_predictions.append(predictions.cpu())
            all_targets.append(targets.cpu())
    
    # Concatenate all predictions and targets
    all_predictions = torch.cat(all_predictions, dim=0)
    all_targets = torch.cat(all_targets, dim=0)
    
    # Calculate metrics
    metrics = calculate_metrics(all_targets, all_predictions, denormalize, scaler)
    average_loss = total_loss / num_batches
    
    return metrics, average_loss


def print_metrics(metrics: Dict[str, float], title: str = "Evaluation Metrics"):
    """
    Print evaluation metrics in a formatted way.
    
    Args:
        metrics: Dictionary of metrics to print.
        title: Title for the metrics display.
    """
    print(f"\n{title}")
    print("=" * len(title))
    
    # Primary metrics
    print(f"MSE:     {metrics['MSE']:.6f}")
    print(f"MAE:     {metrics['MAE']:.6f}")
    print(f"RMSE:    {metrics['RMSE']:.6f}")
    print(f"CV-RMSE: {metrics['CV_RMSE']:.6f}")
    print(f"R²:      {metrics['R2']:.6f}")
    print(f"MAPE:    {metrics['MAPE']:.2f}%")
    print(f"NRMSE:   {metrics['NRMSE']:.6f}")
    
    # Additional statistics
    print(f"\nData Statistics:")
    print(f"True - Mean: {metrics['Mean_True']:.4f}, Std: {metrics['Std_True']:.4f}")
    print(f"Pred - Mean: {metrics['Mean_Pred']:.4f}, Std: {metrics['Std_Pred']:.4f}")


class EarlyStopping:
    """
    Early stopping utility to stop training when validation loss stops improving.
    """
    
    def __init__(self, patience: int = 10, min_delta: float = 0.0, 
                 restore_best_weights: bool = True):
        """
        Initialize early stopping.
        
        Args:
            patience: Number of epochs to wait before stopping.
            min_delta: Minimum change to qualify as an improvement.
            restore_best_weights: Whether to restore best weights when stopping.
        """
        self.patience = patience
        self.min_delta = min_delta
        self.restore_best_weights = restore_best_weights
        
        self.best_loss = float('inf')
        self.counter = 0
        self.best_weights = None
        
    def __call__(self, val_loss: float, model: nn.Module) -> bool:
        """
        Check if training should stop.
        
        Args:
            val_loss: Current validation loss.
            model: Model being trained.
            
        Returns:
            True if training should stop, False otherwise.
        """
        if val_loss < self.best_loss - self.min_delta:
            self.best_loss = val_loss
            self.counter = 0
            if self.restore_best_weights:
                self.best_weights = model.state_dict().copy()
        else:
            self.counter += 1
            
        if self.counter >= self.patience:
            if self.restore_best_weights and self.best_weights is not None:
                model.load_state_dict(self.best_weights)
            return True
            
        return False
