#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Cabin: A Collaborative and Adaptive Framework for Wind Power Forecasting

This package provides a deep learning framework for wind power forecasting that
integrates historical power data with ambient variables through collaborative
and adaptive modeling.
"""

__version__ = "1.0.0"
__author__ = "<PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>"
__email__ = "<EMAIL>"

from .models.cabin import Cabin
from .models.components import AmbientRepresentationModule, CollaborationAmbientVariables
from .models.base import GAR, KAN
from .utils.preprocessing import normalize_data, create_windows, prepare_data
from .utils.metrics import calculate_metrics, evaluate_model

__all__ = [
    "Cabin",
    "AmbientRepresentationModule", 
    "CollaborationAmbientVariables",
    "GAR",
    "KAN",
    "normalize_data",
    "create_windows", 
    "prepare_data",
    "calculate_metrics",
    "evaluate_model",
]
