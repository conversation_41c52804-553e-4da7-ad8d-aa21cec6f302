#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Unit tests for base components (GAR, KAN, activation functions).
"""

import pytest
import torch
import numpy as np
from cabin.models.base import GAR, KAN, KANLinear, get_activation_cls, Sin


class TestGAR:
    """Test cases for Global Autoregression (GAR)."""
    
    def test_gar_initialization(self):
        """Test GAR initialization."""
        gar = GAR(input_window_size=10, output_window_size=1)
        
        assert gar.input_window_size == 10
        assert gar.output_window_size == 1
        assert hasattr(gar, 'l1')
        assert hasattr(gar, 'activate')
    
    def test_gar_forward(self):
        """Test GAR forward pass."""
        gar = GAR(input_window_size=10, output_window_size=3)
        
        batch_size = 4
        input_vars = 2
        x = torch.randn(batch_size, 10, input_vars)
        
        output = gar(x)
        
        assert output.shape == (batch_size, 3, input_vars)
        assert not torch.isnan(output).any()
    
    def test_gar_with_mask(self):
        """Test GAR with input mask."""
        gar = GAR(input_window_size=10, output_window_size=1)
        
        batch_size = 2
        input_vars = 1
        x = torch.randn(batch_size, 10, input_vars)
        mask = torch.ones_like(x, dtype=torch.bool)
        mask[:, :3, :] = False  # Mask first 3 time steps
        
        output = gar(x, mask)
        
        assert output.shape == (batch_size, 1, input_vars)
        assert not torch.isnan(output).any()
    
    def test_gar_different_activations(self):
        """Test GAR with different activation functions."""
        activations = ['linear', 'relu', 'tanh', 'sigmoid']
        
        for activation in activations:
            gar = GAR(input_window_size=5, output_window_size=1, activation=activation)
            x = torch.randn(2, 5, 1)
            output = gar(x)
            
            assert output.shape == (2, 1, 1)
            assert not torch.isnan(output).any()


class TestKANLinear:
    """Test cases for KAN Linear layer."""
    
    def test_kan_linear_initialization(self):
        """Test KANLinear initialization."""
        kan_layer = KANLinear(in_features=4, out_features=2)
        
        assert kan_layer.in_features == 4
        assert kan_layer.out_features == 2
        assert hasattr(kan_layer, 'base_weight')
        assert hasattr(kan_layer, 'spline_weight')
        assert hasattr(kan_layer, 'grid')
    
    def test_kan_linear_forward(self):
        """Test KANLinear forward pass."""
        kan_layer = KANLinear(in_features=3, out_features=2)
        
        batch_size = 5
        x = torch.randn(batch_size, 3)
        
        output = kan_layer(x)
        
        assert output.shape == (batch_size, 2)
        assert not torch.isnan(output).any()
    
    def test_kan_linear_b_splines(self):
        """Test B-spline computation."""
        kan_layer = KANLinear(in_features=2, out_features=1, grid_size=5)
        
        x = torch.randn(3, 2)
        splines = kan_layer.b_splines(x)
        
        expected_shape = (3, 2, kan_layer.grid_size + kan_layer.spline_order)
        assert splines.shape == expected_shape
        assert not torch.isnan(splines).any()
    
    def test_kan_linear_regularization(self):
        """Test KANLinear regularization loss."""
        kan_layer = KANLinear(in_features=2, out_features=1)
        
        reg_loss = kan_layer.regularization_loss()
        
        assert isinstance(reg_loss, torch.Tensor)
        assert reg_loss.item() >= 0


class TestKAN:
    """Test cases for full KAN network."""
    
    def test_kan_initialization(self):
        """Test KAN initialization."""
        kan = KAN(
            input_window_size=10,
            input_vars=2,
            output_window_size=1,
            output_vars=1,
            layers_hidden=[8]
        )
        
        assert kan.input_window_size == 10
        assert kan.input_size == 2
        assert kan.output_window_size == 1
        assert kan.output_size == 1
        assert len(kan.layers) > 0
    
    def test_kan_forward_patch_form(self):
        """Test KAN forward pass with patch form."""
        kan = KAN(
            input_window_size=5,
            input_vars=2,
            output_window_size=3,
            output_vars=2,
            layers_hidden=[4],
            patch_form=True
        )
        
        batch_size = 3
        x = torch.randn(batch_size, 5, 2)
        
        output = kan(x)
        
        assert output.shape == (batch_size, 3, 2)
        assert not torch.isnan(output).any()
    
    def test_kan_forward_no_patch_form(self):
        """Test KAN forward pass without patch form."""
        kan = KAN(
            input_window_size=5,
            input_vars=2,
            output_window_size=3,
            output_vars=1,
            layers_hidden=[8],
            patch_form=False
        )
        
        batch_size = 3
        x = torch.randn(batch_size, 5, 2)
        
        output = kan(x)
        
        assert output.shape == (batch_size, 3, 1)
        assert not torch.isnan(output).any()
    
    def test_kan_regularization(self):
        """Test KAN regularization loss."""
        kan = KAN(
            input_window_size=5,
            input_vars=1,
            output_window_size=1,
            output_vars=1,
            layers_hidden=[4]
        )
        
        reg_loss = kan.regularization_loss()
        
        assert isinstance(reg_loss, torch.Tensor)
        assert reg_loss.item() >= 0


class TestActivationFunctions:
    """Test cases for activation functions."""
    
    def test_get_activation_cls(self):
        """Test activation function retrieval."""
        activations = ['relu', 'gelu', 'tanh', 'sigmoid', 'silu', 'linear', 'sin']
        
        for activation in activations:
            activation_cls = get_activation_cls(activation)
            activation_fn = activation_cls()
            
            x = torch.randn(3, 4)
            output = activation_fn(x)
            
            assert output.shape == x.shape
            assert not torch.isnan(output).any()
    
    def test_sin_activation(self):
        """Test custom Sin activation function."""
        sin_fn = Sin()
        
        x = torch.tensor([0., np.pi/2, np.pi])
        output = sin_fn(x)
        
        expected = torch.sin(x)
        assert torch.allclose(output, expected, atol=1e-6)
    
    def test_unknown_activation(self):
        """Test that unknown activation defaults to linear."""
        activation_cls = get_activation_cls('unknown_activation')
        activation_fn = activation_cls()
        
        x = torch.randn(2, 3)
        output = activation_fn(x)
        
        # Should be identity (linear)
        assert torch.allclose(output, x)


if __name__ == "__main__":
    pytest.main([__file__])
