#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Unit tests for utility functions.
"""

import pytest
import torch
import numpy as np
import pandas as pd
from sklearn.preprocessing import MinMaxScaler

from cabin.utils.preprocessing import (
    normalize_data, denormalize_data, create_windows, 
    WindPowerDataset, load_sample_data
)
from cabin.utils.metrics import calculate_metrics, EarlyStopping


class TestPreprocessing:
    """Test cases for preprocessing utilities."""
    
    def test_normalize_data_numpy(self):
        """Test data normalization with numpy array."""
        data = np.random.randn(100, 3) * 10 + 5
        
        normalized, scaler = normalize_data(data)
        
        assert normalized.shape == data.shape
        assert normalized.min() >= 0
        assert normalized.max() <= 1
        assert isinstance(scaler, MinMaxScaler)
    
    def test_normalize_data_pandas(self):
        """Test data normalization with pandas DataFrame."""
        data = pd.DataFrame(np.random.randn(100, 3) * 10 + 5)
        
        normalized, scaler = normalize_data(data)
        
        assert normalized.shape == data.shape
        assert normalized.min() >= 0
        assert normalized.max() <= 1
    
    def test_denormalize_data(self):
        """Test data denormalization."""
        data = np.random.randn(50, 2) * 5 + 10
        
        normalized, scaler = normalize_data(data)
        denormalized = denormalize_data(normalized, scaler)
        
        assert np.allclose(data, denormalized, atol=1e-10)
    
    def test_create_windows(self):
        """Test sliding window creation."""
        power_data = np.random.randn(100, 2)
        ambient_data = np.random.randn(100, 3)
        
        power_windows, ambient_windows, targets = create_windows(
            power_data, ambient_data, window_size=10, horizon=1
        )
        
        expected_windows = 100 - 10 - 1 + 1  # 90 windows
        assert power_windows.shape == (expected_windows, 10, 2)
        assert ambient_windows.shape == (expected_windows, 10, 3)
        assert targets.shape == (expected_windows, 1, 2)
    
    def test_create_windows_no_ambient(self):
        """Test window creation without ambient data."""
        power_data = np.random.randn(50, 1)
        
        power_windows, ambient_windows, targets = create_windows(
            power_data, window_size=5, horizon=2
        )
        
        expected_windows = 50 - 5 - 2 + 1  # 44 windows
        assert power_windows.shape == (expected_windows, 5, 1)
        assert ambient_windows is None
        assert targets.shape == (expected_windows, 2, 1)
    
    def test_create_windows_insufficient_data(self):
        """Test window creation with insufficient data."""
        power_data = np.random.randn(5, 1)
        
        with pytest.raises(ValueError, match="Not enough data"):
            create_windows(power_data, window_size=10, horizon=1)
    
    def test_wind_power_dataset(self):
        """Test WindPowerDataset class."""
        power_windows = np.random.randn(20, 10, 2)
        targets = np.random.randn(20, 1, 2)
        ambient_windows = np.random.randn(20, 10, 3)
        
        dataset = WindPowerDataset(power_windows, targets, ambient_windows)
        
        assert len(dataset) == 20
        
        # Test with ambient data
        power, ambient, target = dataset[0]
        assert power.shape == (10, 2)
        assert ambient.shape == (10, 3)
        assert target.shape == (1, 2)
        
        # Test without ambient data
        dataset_no_ambient = WindPowerDataset(power_windows, targets)
        power, target = dataset_no_ambient[0]
        assert power.shape == (10, 2)
        assert target.shape == (1, 2)
    
    def test_load_sample_data(self):
        """Test sample data loading."""
        power_data, ambient_data = load_sample_data("synthetic")
        
        assert power_data.shape[0] == 365  # 365 days
        assert power_data.shape[1] == 1    # 1 turbine
        assert ambient_data.shape[0] == 365
        assert ambient_data.shape[1] == 4  # 4 ambient variables
        
        # Check data is normalized
        assert power_data.min() >= 0
        assert power_data.max() <= 1
        assert ambient_data.min() >= 0
        assert ambient_data.max() <= 1
    
    def test_load_sample_data_unknown(self):
        """Test loading unknown dataset."""
        with pytest.raises(ValueError, match="Unknown dataset"):
            load_sample_data("unknown_dataset")


class TestMetrics:
    """Test cases for evaluation metrics."""
    
    def test_calculate_metrics_basic(self):
        """Test basic metrics calculation."""
        y_true = np.array([1.0, 2.0, 3.0, 4.0, 5.0])
        y_pred = np.array([1.1, 1.9, 3.1, 3.9, 5.1])
        
        metrics = calculate_metrics(y_true, y_pred)
        
        assert 'MSE' in metrics
        assert 'MAE' in metrics
        assert 'RMSE' in metrics
        assert 'CV_RMSE' in metrics
        assert 'R2' in metrics
        assert 'MAPE' in metrics
        assert 'NRMSE' in metrics
        
        assert metrics['MSE'] > 0
        assert metrics['MAE'] > 0
        assert metrics['R2'] <= 1
    
    def test_calculate_metrics_torch(self):
        """Test metrics calculation with torch tensors."""
        y_true = torch.tensor([[1.0, 2.0], [3.0, 4.0]])
        y_pred = torch.tensor([[1.1, 1.9], [3.1, 3.9]])
        
        metrics = calculate_metrics(y_true, y_pred)
        
        assert isinstance(metrics['MSE'], float)
        assert metrics['MSE'] > 0
    
    def test_calculate_metrics_perfect_prediction(self):
        """Test metrics with perfect predictions."""
        y_true = np.array([1.0, 2.0, 3.0, 4.0])
        y_pred = y_true.copy()
        
        metrics = calculate_metrics(y_true, y_pred)
        
        assert metrics['MSE'] == 0
        assert metrics['MAE'] == 0
        assert metrics['RMSE'] == 0
        assert metrics['R2'] == 1.0
    
    def test_calculate_metrics_multidimensional(self):
        """Test metrics with multidimensional data."""
        y_true = np.random.randn(10, 3, 2)
        y_pred = y_true + 0.1 * np.random.randn(10, 3, 2)
        
        metrics = calculate_metrics(y_true, y_pred)
        
        assert all(key in metrics for key in ['MSE', 'MAE', 'R2'])
        assert metrics['MSE'] > 0
    
    def test_early_stopping(self):
        """Test early stopping utility."""
        early_stopping = EarlyStopping(patience=3, min_delta=0.01)
        
        # Create a dummy model
        model = torch.nn.Linear(1, 1)
        
        # Simulate improving validation loss
        assert not early_stopping(1.0, model)
        assert not early_stopping(0.9, model)
        assert not early_stopping(0.8, model)
        
        # Simulate worsening validation loss
        assert not early_stopping(0.85, model)  # counter = 1
        assert not early_stopping(0.86, model)  # counter = 2
        assert not early_stopping(0.87, model)  # counter = 3
        assert early_stopping(0.88, model)      # Should stop
    
    def test_early_stopping_restore_weights(self):
        """Test early stopping with weight restoration."""
        early_stopping = EarlyStopping(patience=2, restore_best_weights=True)
        
        model = torch.nn.Linear(1, 1)
        initial_weight = model.weight.data.clone()
        
        # Best loss
        early_stopping(0.5, model)
        best_weight = model.weight.data.clone()
        
        # Modify weights
        model.weight.data.fill_(999.0)
        
        # Trigger early stopping
        early_stopping(1.0, model)  # counter = 1
        early_stopping(1.1, model)  # counter = 2
        should_stop = early_stopping(1.2, model)  # Should stop and restore
        
        assert should_stop
        # Weights should be restored to best
        assert torch.allclose(model.weight.data, best_weight)


if __name__ == "__main__":
    pytest.main([__file__])
