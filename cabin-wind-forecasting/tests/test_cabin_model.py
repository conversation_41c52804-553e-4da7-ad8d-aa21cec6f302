#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Unit tests for the Cabin model.
"""

import pytest
import torch
import numpy as np
from cabin import Cabin
from cabin.models.components import AmbientRepresentationModule, CollaborationAmbientVariables
from cabin.models.base import GAR, KAN


class TestCabinModel:
    """Test cases for the main Cabin model."""
    
    def test_cabin_initialization_only_target(self):
        """Test Cabin model initialization in only_target mode."""
        model = Cabin(
            input_window_size=10,
            input_vars=1,
            output_window_size=1,
            output_vars=1,
            mode='only_target'
        )
        
        assert model.mode == 'only_target'
        assert model.input_window_size == 10
        assert model.input_vars == 1
        assert model.output_window_size == 1
        assert model.output_vars == 1
        assert hasattr(model, 'arm0')
        assert hasattr(model, 'arm1')
        assert hasattr(model, 'arm2')
        assert hasattr(model, 'cav')
    
    def test_cabin_initialization_data_first(self):
        """Test Cabin model initialization in data_first mode."""
        model = Cabin(
            input_window_size=10,
            input_vars=1,
            output_window_size=1,
            output_vars=1,
            ex_vars=4,
            mode='data_first'
        )
        
        assert model.mode == 'data_first'
        assert model.ex_vars == 4
        assert hasattr(model, 'arm0')
        assert hasattr(model, 'cav')
    
    def test_cabin_initialization_learning_first(self):
        """Test Cabin model initialization in learning_first mode."""
        model = Cabin(
            input_window_size=10,
            input_vars=1,
            output_window_size=1,
            output_vars=1,
            ex_vars=4,
            mode='learning_first'
        )
        
        assert model.mode == 'learning_first'
        assert hasattr(model, 'arm0')
        assert hasattr(model, 'ex_arm0')
        assert hasattr(model, 'cav')
    
    def test_cabin_forward_only_target(self):
        """Test forward pass in only_target mode."""
        model = Cabin(
            input_window_size=10,
            input_vars=2,
            output_window_size=1,
            output_vars=2,
            mode='only_target'
        )
        
        batch_size = 4
        x = torch.randn(batch_size, 10, 2)
        
        output = model(x)
        
        assert output.shape == (batch_size, 1, 2)
        assert not torch.isnan(output).any()
    
    def test_cabin_forward_data_first(self):
        """Test forward pass in data_first mode."""
        model = Cabin(
            input_window_size=10,
            input_vars=1,
            output_window_size=1,
            output_vars=1,
            ex_vars=3,
            mode='data_first'
        )
        
        batch_size = 4
        x = torch.randn(batch_size, 10, 1)
        e = torch.randn(batch_size, 10, 3)
        
        output = model(x, e)
        
        assert output.shape == (batch_size, 1, 1)
        assert not torch.isnan(output).any()
    
    def test_cabin_forward_learning_first(self):
        """Test forward pass in learning_first mode."""
        model = Cabin(
            input_window_size=10,
            input_vars=1,
            output_window_size=1,
            output_vars=1,
            ex_vars=3,
            mode='learning_first'
        )
        
        batch_size = 4
        x = torch.randn(batch_size, 10, 1)
        e = torch.randn(batch_size, 10, 3)
        
        output = model(x, e)
        
        assert output.shape == (batch_size, 1, 1)
        assert not torch.isnan(output).any()
    
    def test_cabin_invalid_mode(self):
        """Test that invalid mode raises ValueError."""
        with pytest.raises(ValueError, match="Unknown mode"):
            Cabin(
                input_window_size=10,
                input_vars=1,
                output_window_size=1,
                output_vars=1,
                mode='invalid_mode'
            )
    
    def test_cabin_missing_ambient_data(self):
        """Test that missing ambient data raises error when required."""
        model = Cabin(
            input_window_size=10,
            input_vars=1,
            output_window_size=1,
            output_vars=1,
            ex_vars=3,
            mode='data_first'
        )
        
        x = torch.randn(4, 10, 1)
        
        with pytest.raises(ValueError, match="Ambient variable data 'e' is required"):
            model(x)
    
    def test_cabin_get_model_info(self):
        """Test model info retrieval."""
        model = Cabin(
            input_window_size=10,
            input_vars=1,
            output_window_size=1,
            output_vars=1,
            ex_vars=3,
            hidden_size=32,
            mode='data_first'
        )
        
        info = model.get_model_info()
        
        assert info['model_name'] == 'Cabin'
        assert info['mode'] == 'data_first'
        assert info['input_window_size'] == 10
        assert info['hidden_size'] == 32
        assert 'total_parameters' in info
        assert 'trainable_parameters' in info
    
    def test_cabin_softmax_configurations(self):
        """Test different ARM softmax configurations."""
        configs = [
            (True, True, True),
            (True, False, False),
            (False, True, False),
            (False, False, True),
            (False, False, False)
        ]
        
        for sample, temporal, feature in configs:
            model = Cabin(
                input_window_size=10,
                input_vars=1,
                output_window_size=1,
                output_vars=1,
                mode='only_target',
                use_sample_softmax=sample,
                use_temporal_softmax=temporal,
                use_feature_wise_softmax=feature
            )
            
            assert model.use_arm0 == sample
            assert model.use_arm1 == temporal
            assert model.use_arm2 == feature
            
            # Test forward pass
            x = torch.randn(2, 10, 1)
            output = model(x)
            assert output.shape == (2, 1, 1)


class TestAmbientRepresentationModule:
    """Test cases for the ARM component."""
    
    def test_arm_initialization(self):
        """Test ARM initialization."""
        arm = AmbientRepresentationModule(
            window_size=10,
            input_size=4,
            dim=1,
            use_activate=True
        )
        
        assert arm.window_size == 10
        assert arm.input_size == 4
        assert arm.dim == 1
        assert arm.use_activate == True
        assert arm.weight.shape == (10, 4)
    
    def test_arm_forward(self):
        """Test ARM forward pass."""
        arm = AmbientRepresentationModule(
            window_size=10,
            input_size=4,
            dim=1
        )
        
        batch_size = 3
        x = torch.randn(batch_size, 10, 4)
        
        output = arm(x)
        
        assert output.shape == (batch_size, 10, 4)
        assert not torch.isnan(output).any()
    
    def test_arm_no_softmax(self):
        """Test ARM with no softmax (dim=-1)."""
        arm = AmbientRepresentationModule(
            window_size=10,
            input_size=4,
            dim=-1
        )
        
        x = torch.randn(3, 10, 4)
        output = arm(x)
        
        assert output.shape == (3, 10, 4)


class TestCollaborationAmbientVariables:
    """Test cases for the CAV component."""
    
    def test_cav_initialization(self):
        """Test CAV initialization."""
        cav = CollaborationAmbientVariables(
            input_window_size=10,
            output_window_size=1,
            input_size=8,
            hidden_size=32,
            output_size=1
        )
        
        assert hasattr(cav, 'mapping')
        assert hasattr(cav, 'ar')
        assert hasattr(cav, 'kan')
        assert isinstance(cav.ar, GAR)
        assert isinstance(cav.kan, KAN)
    
    def test_cav_forward(self):
        """Test CAV forward pass."""
        cav = CollaborationAmbientVariables(
            input_window_size=10,
            output_window_size=1,
            input_size=8,
            hidden_size=32,
            output_size=2
        )
        
        batch_size = 4
        x = torch.randn(batch_size, 10, 8)
        
        output = cav(x)
        
        assert output.shape == (batch_size, 1, 2)
        assert not torch.isnan(output).any()


if __name__ == "__main__":
    pytest.main([__file__])
