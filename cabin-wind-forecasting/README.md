# Cabin: A Collaborative and Adaptive Framework for Wind Power Forecasting

[![Python 3.8+](https://img.shields.io/badge/python-3.8+-blue.svg)](https://www.python.org/downloads/)
[![PyTorch](https://img.shields.io/badge/PyTorch-1.9+-red.svg)](https://pytorch.org/)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)

## Overview

Cabin is a novel deep learning framework for wind power forecasting that effectively integrates historical wind power data with ambient variables (temperature, wind speed, direction, etc.) to enhance prediction accuracy. The model features a collaborative and adaptive architecture designed to handle varying levels of data completeness in real-world applications.

## Key Features

- **Collaborative Integration**: Sophisticated joint modeling of historical power data and ambient variables
- **Adaptive Architecture**: Three distinct configurations to accommodate different data availability scenarios
- **Advanced Components**: 
  - Ambient Representation Module (ARM) for multi-dimensional feature extraction
  - Collaboration of Ambient Variables (CAV) module with Kolmogorov-Arnold Networks (KAN)
- **Superior Performance**: Demonstrated improvements over 34 baseline models on wind power datasets

## Architecture Variants

1. **Only-Target**: Uses exclusively historical wind power data
2. **Data-First**: Early fusion of power and ambient data
3. **Learning-First**: Separate feature learning followed by fusion

## Installation

### Requirements

- Python 3.8+
- PyTorch 1.9+
- NumPy
- Pandas
- Scikit-learn

### Install from Source

```bash
git clone https://github.com/yourusername/cabin-wind-forecasting.git
cd cabin-wind-forecasting
pip install -e .
```

### Install from PyPI

```bash
pip install cabin-wind-forecasting
```

## Quick Start

```python
import torch
from cabin import Cabin

# Initialize model for data-first configuration
model = Cabin(
    input_window_size=10,      # 10 days of historical data
    input_vars=1,              # Number of wind turbines/power series
    output_window_size=1,      # 1 day forecast horizon
    output_vars=1,             # Number of output variables
    ex_vars=4,                 # Number of ambient variables (temp, wind speed, etc.)
    hidden_size=64,            # Hidden layer size
    mode='data_first'          # Architecture variant
)

# Example input data
batch_size = 8
historical_power = torch.randn(batch_size, 10, 1)  # [batch, time, features]
ambient_vars = torch.randn(batch_size, 10, 4)      # [batch, time, ambient_features]

# Forward pass
predictions = model(historical_power, ambient_vars)
print(f"Predictions shape: {predictions.shape}")  # [batch, horizon, output_vars]
```

## Model Configurations

### Only-Target Configuration
```python
model = Cabin(
    input_window_size=10,
    input_vars=1,
    output_window_size=1,
    output_vars=1,
    mode='only_target'
)

# Only historical power data needed
predictions = model(historical_power)
```

### Data-First Configuration
```python
model = Cabin(
    input_window_size=10,
    input_vars=1,
    output_window_size=1,
    output_vars=1,
    ex_vars=4,
    mode='data_first'
)

predictions = model(historical_power, ambient_vars)
```

### Learning-First Configuration
```python
model = Cabin(
    input_window_size=10,
    input_vars=1,
    output_window_size=1,
    output_vars=1,
    ex_vars=4,
    mode='learning_first'
)

predictions = model(historical_power, ambient_vars)
```

## Training Example

```python
import torch.nn as nn
import torch.optim as optim
from cabin import Cabin
from cabin.utils import prepare_data

# Load and prepare your data
train_loader, val_loader, test_loader = prepare_data(
    power_data='path/to/power_data.csv',
    ambient_data='path/to/ambient_data.csv',
    window_size=10,
    horizon=1,
    batch_size=32
)

# Initialize model
model = Cabin(
    input_window_size=10,
    input_vars=1,
    output_window_size=1,
    output_vars=1,
    ex_vars=4,
    mode='data_first'
)

# Training setup
criterion = nn.MSELoss()
optimizer = optim.Adam(model.parameters(), lr=0.001)

# Training loop
model.train()
for epoch in range(100):
    for batch_idx, (power_data, ambient_data, targets) in enumerate(train_loader):
        optimizer.zero_grad()
        predictions = model(power_data, ambient_data)
        loss = criterion(predictions, targets)
        loss.backward()
        optimizer.step()
    
    print(f'Epoch {epoch+1}, Loss: {loss.item():.4f}')
```

## Data Format

### Input Data Requirements

1. **Historical Power Data**: Shape `[batch_size, window_size, num_turbines]`
2. **Ambient Variables**: Shape `[batch_size, window_size, num_ambient_vars]`

### Supported Ambient Variables

- Wind speed
- Wind direction  
- Temperature
- Pressure
- Humidity
- Any other meteorological variables

### Data Preprocessing

```python
from cabin.preprocessing import normalize_data, create_windows

# Normalize data to [0, 1] range
power_normalized = normalize_data(power_data)
ambient_normalized = normalize_data(ambient_data)

# Create sliding windows
power_windows, ambient_windows, targets = create_windows(
    power_normalized, 
    ambient_normalized, 
    window_size=10, 
    horizon=1
)
```

## Examples

See the `examples/` directory for complete working examples:

- `basic_usage.py`: Simple forecasting example
- `training_example.py`: Complete training pipeline
- `evaluation_example.py`: Model evaluation and metrics
- `data_preparation.py`: Data preprocessing utilities

## Performance

Cabin has been evaluated on multiple wind power datasets and consistently outperforms baseline models:

- **Mean Squared Error (MSE)**: Up to 48.63% reduction
- **Coefficient of Variation RMSE (CV-RMSE)**: Up to 28.33% reduction
- **Superior performance** across 34 baseline models including LSTM, Transformer, and Graph Neural Networks

## Citation

If you use Cabin in your research, please cite our paper:

```bibtex
@article{wu2024cabin,
  title={Cabin: A Collaborative and Adaptive Framework for Wind Power Forecasting Integrating Ambient Variables},
  author={Wu, Senzhen and Chen, Yu and He, Xinhao and Wang, Zhijin and Liu, Xiufeng and Fu, Yonggang},
  journal={Energy},
  year={2024}
}
```

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Contributing

We welcome contributions! Please see [CONTRIBUTING.md](CONTRIBUTING.md) for guidelines.

## Support

For questions and support:
- Open an issue on GitHub
- Contact: <EMAIL>

## Acknowledgments

This work is based on research conducted at Jimei University and Technical University of Denmark. We thank the wind power forecasting community for providing datasets and benchmarks.
